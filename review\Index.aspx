﻿<%@ Page Title="เรารวบรวม สื่อ Review เพื่อให้ลูกค้าไปโพสขาย" Language="vb" AutoEventWireup="false" MasterPageFile="~/review/SiteReview.Master" CodeBehind="Index.aspx.vb" Inherits="AmiBigeye.Index" %>


<asp:Content ID="ContentReview" ContentPlaceHolderID="ContentPlaceHolderReview" runat="server">

<main>

  <section class="py-5 text-center container">
    <div class="row py-lg-5">
      <div class="col-lg-6 col-md-8 mx-auto">
        <h1 class="fw-light">Brand</h1>
        <p class="lead text-body-secondary">เลือกยี่ห้อ ที่อยากดูด Review ใหม่ๆ ได้เลยจ้า. เรียงจากการ Update ใหม่ ไป เก่า</p>
        <p>
          <a href="?Brand=Witches Lens" class="btn btn-primary my-2" style="background-color:black">Witches Lens (เลนส์แม่มด)</a>
          <a href="?Brand=Pitchy Lens" class="btn btn-primary my-2">Pitchy Lens</a>
          <a href="?Brand=Pretty Doll" class="btn btn-primary my-2">Pretty Doll</a>
          <a href="?Brand=Kitty <PERSON>" class="btn btn-primary my-2">Kitty Kawaii</a>
          <a href="?Brand=Sweety Plus" class="btn btn-primary my-2">Sweety Plus</a>
        </p>
      </div>
    </div>
  </section>

  <div class="album py-5 bg-body-tertiary">
    <div class="container">

      <div class="row row-cols-1 row-cols-sm-2 row-cols-md-3 g-3">

<%
    For y As Integer = 0 To (tom_y - 2)
%>
        <div class="col"><a href="Download.aspx?Model=<%=tom_Distinct_filename(y)%>" target="_blank">
          <div class="card shadow-sm">
            <!--  รอแก้ไข _Gray  เมื่อเพลง ทำไฟล์ภาพเสร็จ (_Black จะไม่ออก)  -->
            <img src="https://www.amibigeye.com/!Review/<%=tom_ReturnReplace_Distinct_filename(y)%>.jpg"/>
            <div class="card-body">
                <!--  รอแก้ไข  (Witches Lens)   -->
              <p class="card-text"><%=tom_Distinct_filename(y)%></p>
              <%-- <div class="d-flex justify-content-between align-items-center">
               <div class="btn-group">
                  <button type="button" class="btn btn-sm btn-outline-secondary">Picture</button>
                  <button type="button" class="btn btn-sm btn-outline-secondary">Video</button>
                </div>
                <small class="text-body-secondary">9 Item</small>
              </div>--%>
            </div>
          </div>
        </a></div>
<%
    Next
%>
      </div>
    </div>
  </div>

</main>

</asp:Content>
