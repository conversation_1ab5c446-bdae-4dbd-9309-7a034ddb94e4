.ip-header {
	position: fixed;
	top: 0;
	z-index: 100;
	min-height: 460px;
	width: 100%;
	height: 100%;
	background: #f1f1f1;
}

.ip-header h1 {
	margin: 0;
}

.ip-logo,
.ip-loader {
	position: absolute;
	left: 0;
	width: 100%;
	opacity: 0;
	cursor: default;
	pointer-events: none;
}

.ip-logo {
	top: 0;
	height: 100%;
	-webkit-transform: translate3d(0,25%,0);
	transform: translate3d(0,25%,0);
}

.ip-loader {
	bottom: 40%;
}

.ip-header .ip-inner {
	display: block;
	margin: 0 auto;
}

.ip-header .ip-logo svg {
	position: absolute;
	min-height: 200px;
	height: 25%;
}

.loaded .ip-header .ip-logo svg {
	max-height: 300px;
}

.ip-header .ip-logo svg path {
	fill: #1c9ccd;
}

.ip-header .ip-loader svg path {
	fill: none;
	stroke-width: 6;
}

.ip-header .ip-loader svg path.ip-loader-circlebg {
	stroke: #ddd;
}

.ip-header .ip-loader svg path.ip-loader-circle {
	-webkit-transition: stroke-dashoffset 0.2s;
	transition: stroke-dashoffset 0.2s;
	stroke: #f88c91;
}

/* Content */

.browser {
	margin: 0 auto;
	padding-top: 8%;
	min-height: 400px;
	max-width: 1000px;
	width: 100%;
	border-radius: 8px;
	background: #fff url(../img/browser.png) no-repeat 50% 0;
	background-size: 100%;
	color: #d3d3d3;
}

.box {
	float: left;
	padding: 3.5em;
	width: 33.3%;
	font-size: 0.7em;
	line-height: 1.5;
}

.box p {
	font-family: 'Blokk', Arial, sans-serif;
}

/* Animations */

/* Initial animation of header elements */
.loading .ip-logo,
.loading .ip-loader {
	opacity: 1;
}

.loading .ip-logo {
	-webkit-animation: animInitialLogo 1s cubic-bezier(0.7,0,0.3,1) both;
	animation: animInitialLogo 1s cubic-bezier(0.7,0,0.3,1) both;
}

@-webkit-keyframes animInitialLogo {
	from { opacity: 0; }
}

@keyframes animInitialLogo {
	from { opacity: 0; }
}

.loading .ip-loader {
	-webkit-animation: animInitialLoader 1s cubic-bezier(0.7,0,0.3,1) both;
	animation: animInitialLoader 1s cubic-bezier(0.7,0,0.3,1) both;
}

@-webkit-keyframes animInitialLoader {
	from { opacity: 0; -webkit-transform: scale3d(0.5,0.5,1); }
}

@keyframes animInitialLoader {
	from { opacity: 0; -webkit-transform: scale3d(0.5,0.5,1); transform: scale3d(0.5,0.5,1); }
}

/* Header elements when loading finishes */
.loaded .ip-logo,
.loaded .ip-loader {
	opacity: 1;
}

.loaded .ip-logo {
	-webkit-transform-origin: 50% 0;
	transform-origin: 50% 0;
	-webkit-animation: animLoadedLogo 1s cubic-bezier(0.7,0,0.3,1) forwards;
	animation: animLoadedLogo 1s cubic-bezier(0.7,0,0.3,1) forwards;
}

@-webkit-keyframes animLoadedLogo {
	to { -webkit-transform: translate3d(0,100%,0) translate3d(0,50px,0) scale3d(0.65,0.65,1); }
}

@keyframes animLoadedLogo {
	to { -webkit-transform: translate3d(0,100%,0) translate3d(0,50px,0) scale3d(0.65,0.65,1); transform: translate3d(0,100%,0) translate3d(0,50px,0) scale3d(0.65,0.65,1); }
}

.loaded .ip-loader {
	-webkit-animation: animLoadedLoader 0.5s cubic-bezier(0.7,0,0.3,1) forwards;
	animation: animLoadedLoader 0.5s cubic-bezier(0.7,0,0.3,1) forwards;
}

@-webkit-keyframes animLoadedLoader {
	to { opacity: 0; -webkit-transform: translate3d(0,-100%,0) scale3d(0.3,0.3,1); }
}

@keyframes animLoadedLoader {
	to { opacity: 0; -webkit-transform: translate3d(0,-100%,0) scale3d(0.3,0.3,1); transform: translate3d(0,-100%,0) scale3d(0.3,0.3,1); }
}

/* Change the color of the logo */
.loaded .ip-logo svg path {
	-webkit-transition: all 0.5s ease 0.3s;
	transition: all 0.5s ease 0.3s;
	fill: #fff;
}

/* Header animation when loading finishes */
.loaded .ip-header {
	-webkit-animation: animLoadedHeader 1s cubic-bezier(0.7,0,0.3,1) forwards;
	animation: animLoadedHeader 1s cubic-bezier(0.7,0,0.3,1) forwards;
}

@-webkit-keyframes animLoadedHeader {
	to { -webkit-transform: translate3d(0,-100%,0); }
}

@keyframes animLoadedHeader {
	to { -webkit-transform: translate3d(0,-100%,0); transform: translate3d(0,-100%,0); }
}

/* Content animations */
.loaded .ip-main h2,
.loaded .ip-main .browser,
.loaded .ip-main .browser .box,
.loaded .codrops-demos {
	-webkit-animation: animLoadedContent 1s cubic-bezier(0.7,0,0.3,1) both;
	animation: animLoadedContent 1s cubic-bezier(0.7,0,0.3,1) both;
}

.loaded .ip-main h2,
.loaded .ip-main .browser .box:first-child {
	-webkit-animation-delay: 0.1s;
	animation-delay: 0.1s;
}

.loaded .codrops-demos,
.loaded .ip-main .browser .box:nth-child(2) {
	-webkit-animation-delay: 0.15s;
	animation-delay: 0.15s;
}

.loaded .ip-main .browser .box:nth-child(3) {
	-webkit-animation-delay: 0.2s;
	animation-delay: 0.2s;
}

@-webkit-keyframes animLoadedContent {
	from { opacity: 0; -webkit-transform: scale3d(0.3,0.3,1); }
}

@keyframes animLoadedContent {
	from { opacity: 0; -webkit-transform: scale3d(0.3,0.3,1); transform: scale3d(0.3,0.3,1); }
}

/* Change layout class for header */
.layout-switch .ip-header {
	position: absolute;
}

/* No JS */
.no-js .ip-header {
	position: relative;
	min-height: 0px;
}

.no-js .ip-header .ip-logo {
	margin-top: 20px;
	height: 180px;
	opacity: 1;
	-webkit-transform: none;
	transform: none;
}

.no-js .ip-header .ip-logo svg path {
	fill: #fff;
}

/* Media queries */

@media screen and (max-width: 45em) {

	.ip-main h2 {
		font-size: 2.25em;
		font-size: 10vw;
	}

	.box {
		width: 100%%;
	}

}