<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Fixed Photo Album Viewer</title>
    <script src="https://ajax.googleapis.com/ajax/libs/jquery/3.6.0/jquery.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/page-flip@2.0.7/dist/js/page-flip.browser.min.js"></script>
    <style>
        body, html {
            margin: 0;
            padding: 0;
            width: 100%;
            height: 100%;
            overflow: hidden;
            background-color: #722F37; /* สีไวน์แดง */
            font-family: Arial, sans-serif;
        }
        #logo {
            position: fixed;
            top: 20px;
            left: 50%;
            transform: translateX(-50%);
            z-index: 1000;
            max-width: 150px;
            max-height: 60px;
        }
        #album-frame {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 80vmin;
            height: 80vmin;
            max-width: 80vh;
            max-height: 80vh;
            background-color: #f0e6d2; /* สีกระดาษเก่า */
            border: 10px solid #8b4513; /* สีกรอบไม้ */
            box-shadow: 0 0 30px rgba(0, 0, 0, 0.5);
            display: flex;
            justify-content: center;
            align-items: center;
            box-sizing: border-box;
        }
        #book-container {
            width: 100%;
            height: 100%;
            display: flex;
            justify-content: center;
            align-items: center;
        }
        #book {
            width: 100%;
            height: 100%;
            max-width: 100%;
            max-height: 100%;
        }
        .page {
            background-color: white;
        }
        .page img {
            width: 100%;
            height: 100%;
            object-fit: contain;
        }
        #save-button {
            position: fixed;
            bottom: 20px;
            right: 20px;
            background-color: rgba(139, 69, 19, 0.8); /* สีกรอบไม้ */
            color: white;
            border: none;
            border-radius: 50%;
            width: 60px;
            height: 60px;
            font-size: 24px;
            cursor: pointer;
            display: none; /* จะแสดงเฉพาะบนมือถือ */
            z-index: 1000;
            transition: background-color 0.3s;
        }
        #save-button:hover {
            background-color: rgba(139, 69, 19, 1);
        }
        @media (max-width: 768px) {
            #album-frame {
                width: 90vmin;
                height: 90vmin;
                border-width: 5px;
            }
        }
    </style>
</head>
<body>
    <img id="logo" src="https://via.placeholder.com/150x60?text=Your+Logo" alt="Album Logo">
    <div id="album-frame">
        <div id="book-container">
            <div id="book"></div>
        </div>
    </div>
    <button id="save-button" title="Save current image">💾</button>

    <script>
        // สร้างอาร์เรย์ของ URL รูปภาพ (แทนที่ด้วย URL จริงของรูปภาพของคุณ)
        const images = [
            'https://www.amibigeye.com/album/test_album/images/1.jpg',
            'https://www.amibigeye.com/album/test_album/images/2.jpg',
            'https://www.amibigeye.com/album/test_album/images/3.jpg',
            'https://www.amibigeye.com/album/test_album/images/4.jpg',
            'https://www.amibigeye.com/album/test_album/images/5.jpg',
            'https://www.amibigeye.com/album/test_album/images/6.jpg',
        ];

        // สร้างหน้าสำหรับหนังสือ
        const pageHTML = images.map(img => `<div class="page"><img src="${img}" alt="Album page"></div>`).join('');
        document.getElementById('book').innerHTML = pageHTML;

        // สร้าง PageFlip object
        const pageFlip = new St.PageFlip(document.getElementById('book'), {
            width: 1000, // จะถูกปรับตามขนาดหน้าจอ
            height: 1000, // จะถูกปรับตามขนาดหน้าจอ
            size: "fixed",
            minWidth: 100,
            maxWidth: 1000,
            minHeight: 100,
            maxHeight: 1000,
            maxShadowOpacity: 0.5,
            showCover: true,
            mobileScrollSupport: false,
            usePortrait: true,
            flippingTime: 1000,
        });

        // โหลดหน้า
        pageFlip.loadFromHTML(document.querySelectorAll('.page'));

        // ฟังก์ชันปรับขนาดอัลบัม
        function resizeBook() {
            const frame = document.getElementById('album-frame');
            const container = document.getElementById('book-container');
            const book = document.getElementById('book');
            const frameSize = Math.min(frame.offsetWidth, frame.offsetHeight);
            
            container.style.width = `${frameSize}px`;
            container.style.height = `${frameSize}px`;
            book.style.width = `${frameSize}px`;
            book.style.height = `${frameSize}px`;
            pageFlip.updateFromDimensions(frameSize, frameSize);
        }

        // อัปเดตขนาดเมื่อหน้าต่างเปลี่ยนขนาด
        window.addEventListener('resize', resizeBook);

        // เริ่มต้นปรับขนาด
        resizeBook();

        // ฟังก์ชันสำหรับบันทึกรูปภาพ
        function saveCurrentImage() {
            const currentPage = pageFlip.getCurrentPageIndex();
            const imageUrl = images[currentPage];
            
            // สร้าง element a ชั่วคราวเพื่อดาวน์โหลด
            const link = document.createElement('a');
            link.href = imageUrl;
            link.download = `album_page_${currentPage + 1}.jpg`;
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
        }

        // แสดงปุ่ม Save เฉพาะบนมือถือ
        if (/Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent)) {
            document.getElementById('save-button').style.display = 'block';
            document.getElementById('save-button').addEventListener('click', saveCurrentImage);
        }
    </script>
</body>
</html>