﻿<%@ Page Title="Download Picture and Video ได้เลยจ้า" Language="vb" AutoEventWireup="false" MasterPageFile="~/review/SiteReview.Master" CodeBehind="Download.aspx.vb" Inherits="AmiBigeye.Download" %>


<asp:Content ID="ContentDownload" ContentPlaceHolderID="ContentPlaceHolderReview" runat="server">

<main>

<%--  <section class="py-5 text-center container">
    <div class="row py-lg-5">
      <div class="col-lg-6 col-md-8 mx-auto">
        <h1 class="fw-light">Brand</h1>
        <p class="lead text-body-secondary">เลือกยี่ห้อ ที่อยากจะดู Review ใหม่ๆได้เลยจ้า. การเรียงจะเรียงจากการ Update.</p>
        <p>
          <a href="#" class="btn btn-primary my-2">Witches Lens (เลนส์แม่มด)</a>
          <a href="#" class="btn btn-secondary my-2">Pretty Doll</a>
        </p>
      </div>
    </div>
  </section>--%>


  <div class="album py-5 bg-body-tertiary">
    <div class="container">


<nav style="--bs-breadcrumb-divider: url(&#34;data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='8' height='8'%3E%3Cpath d='M2.5 0L1 1.5 3.5 4 1 6.5 2.5 8l4-4-4-4z' fill='%236c757d'/%3E%3C/svg%3E&#34;);" aria-label="breadcrumb">
  <ol class="breadcrumb">
    <li class="breadcrumb-item h5"><a href="../review">Review</a></li>
    <li class="breadcrumb-item active h5" aria-current="page"><%=Model%></li>
  </ol>
</nav>



      <div class="row row-cols-1 row-cols-sm-2 row-cols-md-3 g-3">

<%
    For x As Integer = 0 To (tom_x - 1)

        If Right(tom_filename(x), 4) = ".jpg" Then
%>
        <div class="col">
          <div class="card shadow-sm">
            <!--  รอแก้ไข _Gray   _Black จะไม่ออก  -->
            <img src="https://www.amibigeye.com/!Review/<%=tom_filename(x)%>"/>
            <div class="card-body">
                <!--  รอแก้ไข  (Witches Lens)   -->
              <p class="card-text"><%=tom_filename(x)%></p>
              <%-- <div class="d-flex justify-content-between align-items-center">
               <div class="btn-group">
                  <button type="button" class="btn btn-sm btn-outline-secondary">Picture</button>
                  <button type="button" class="btn btn-sm btn-outline-secondary">Video</button>
                </div>
                <small class="text-body-secondary">9 Item</small>
              </div>--%>
            </div>
          </div>
        </div>
    <%

        ElseIf Right(tom_filename(x), 4) = ".mp4" Then
    %>
         <div class="col">
          <div class="card shadow-sm">
            <!--  รอแก้ไข _Gray   _Black จะไม่ออก  -->
            <video id="myVideo" controls preload="none"><source id="videoSource" data-src="https://www.amibigeye.com/!Review/<%=tom_filename(x)%>" type="video/mp4">เบราว์เซอร์ของคุณไม่รองรับการเล่นวิดีโอแบบนี้</video>

            <div class="card-body">
                <!--  รอแก้ไข  (Witches Lens)   -->
              <p class="card-text"><%=tom_filename(x)%><br /><a href="https://www.amibigeye.com/!Review/<%=tom_filename(x)%>" download>วิดีโอไม่เล่น คลิกที่นี่เพื่อดาวน์โหลดวิดีโอ</a></p>
              <%-- <div class="d-flex justify-content-between align-items-center">
               <div class="btn-group">
                  <button type="button" class="btn btn-sm btn-outline-secondary">Picture</button>
                  <button type="button" class="btn btn-sm btn-outline-secondary">Video</button>
                </div>
                <small class="text-body-secondary">9 Item</small>
              </div>--%>
            </div>
          </div>
        </div>
<%

        End If

    Next
%>

      </div>
    </div>
  </div>

</main>

</asp:Content>
