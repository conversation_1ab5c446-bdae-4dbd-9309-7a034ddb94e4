F:\! ASP.NET Now Writing test\AmiBigeye\AmiBigeye\bin\AmiBigeye.dll.config
F:\! ASP.NET Now Writing test\AmiBigeye\AmiBigeye\bin\AmiBigeye.dll
F:\! ASP.NET Now Writing test\AmiBigeye\AmiBigeye\bin\AmiBigeye.pdb
F:\! ASP.NET Now Writing test\AmiBigeye\AmiBigeye\bin\WebForms_Basic.xml
F:\! ASP.NET Now Writing test\AmiBigeye\AmiBigeye\bin\roslyn\csc.exe
F:\! ASP.NET Now Writing test\AmiBigeye\AmiBigeye\bin\roslyn\csc.exe.config
F:\! ASP.NET Now Writing test\AmiBigeye\AmiBigeye\bin\roslyn\csc.rsp
F:\! ASP.NET Now Writing test\AmiBigeye\AmiBigeye\bin\roslyn\csi.exe
F:\! ASP.NET Now Writing test\AmiBigeye\AmiBigeye\bin\roslyn\csi.exe.config
F:\! ASP.NET Now Writing test\AmiBigeye\AmiBigeye\bin\roslyn\csi.rsp
F:\! ASP.NET Now Writing test\AmiBigeye\AmiBigeye\bin\roslyn\Microsoft.Build.Tasks.CodeAnalysis.dll
F:\! ASP.NET Now Writing test\AmiBigeye\AmiBigeye\bin\roslyn\Microsoft.CodeAnalysis.CSharp.dll
F:\! ASP.NET Now Writing test\AmiBigeye\AmiBigeye\bin\roslyn\Microsoft.CodeAnalysis.CSharp.Scripting.dll
F:\! ASP.NET Now Writing test\AmiBigeye\AmiBigeye\bin\roslyn\Microsoft.CodeAnalysis.dll
F:\! ASP.NET Now Writing test\AmiBigeye\AmiBigeye\bin\roslyn\Microsoft.CodeAnalysis.Scripting.dll
F:\! ASP.NET Now Writing test\AmiBigeye\AmiBigeye\bin\roslyn\Microsoft.CodeAnalysis.VisualBasic.dll
F:\! ASP.NET Now Writing test\AmiBigeye\AmiBigeye\bin\roslyn\Microsoft.CSharp.Core.targets
F:\! ASP.NET Now Writing test\AmiBigeye\AmiBigeye\bin\roslyn\Microsoft.DiaSymReader.Native.amd64.dll
F:\! ASP.NET Now Writing test\AmiBigeye\AmiBigeye\bin\roslyn\Microsoft.DiaSymReader.Native.x86.dll
F:\! ASP.NET Now Writing test\AmiBigeye\AmiBigeye\bin\roslyn\Microsoft.Managed.Core.targets
F:\! ASP.NET Now Writing test\AmiBigeye\AmiBigeye\bin\roslyn\Microsoft.VisualBasic.Core.targets
F:\! ASP.NET Now Writing test\AmiBigeye\AmiBigeye\bin\roslyn\Microsoft.Win32.Primitives.dll
F:\! ASP.NET Now Writing test\AmiBigeye\AmiBigeye\bin\roslyn\System.AppContext.dll
F:\! ASP.NET Now Writing test\AmiBigeye\AmiBigeye\bin\roslyn\System.Collections.Immutable.dll
F:\! ASP.NET Now Writing test\AmiBigeye\AmiBigeye\bin\roslyn\System.Console.dll
F:\! ASP.NET Now Writing test\AmiBigeye\AmiBigeye\bin\roslyn\System.Diagnostics.DiagnosticSource.dll
F:\! ASP.NET Now Writing test\AmiBigeye\AmiBigeye\bin\roslyn\System.Diagnostics.FileVersionInfo.dll
F:\! ASP.NET Now Writing test\AmiBigeye\AmiBigeye\bin\roslyn\System.Diagnostics.StackTrace.dll
F:\! ASP.NET Now Writing test\AmiBigeye\AmiBigeye\bin\roslyn\System.Globalization.Calendars.dll
F:\! ASP.NET Now Writing test\AmiBigeye\AmiBigeye\bin\roslyn\System.IO.Compression.dll
F:\! ASP.NET Now Writing test\AmiBigeye\AmiBigeye\bin\roslyn\System.IO.Compression.ZipFile.dll
F:\! ASP.NET Now Writing test\AmiBigeye\AmiBigeye\bin\roslyn\System.IO.FileSystem.dll
F:\! ASP.NET Now Writing test\AmiBigeye\AmiBigeye\bin\roslyn\System.IO.FileSystem.Primitives.dll
F:\! ASP.NET Now Writing test\AmiBigeye\AmiBigeye\bin\roslyn\System.Net.Http.dll
F:\! ASP.NET Now Writing test\AmiBigeye\AmiBigeye\bin\roslyn\System.Net.Sockets.dll
F:\! ASP.NET Now Writing test\AmiBigeye\AmiBigeye\bin\roslyn\System.Reflection.Metadata.dll
F:\! ASP.NET Now Writing test\AmiBigeye\AmiBigeye\bin\roslyn\System.Runtime.InteropServices.RuntimeInformation.dll
F:\! ASP.NET Now Writing test\AmiBigeye\AmiBigeye\bin\roslyn\System.Security.Cryptography.Algorithms.dll
F:\! ASP.NET Now Writing test\AmiBigeye\AmiBigeye\bin\roslyn\System.Security.Cryptography.Encoding.dll
F:\! ASP.NET Now Writing test\AmiBigeye\AmiBigeye\bin\roslyn\System.Security.Cryptography.Primitives.dll
F:\! ASP.NET Now Writing test\AmiBigeye\AmiBigeye\bin\roslyn\System.Security.Cryptography.X509Certificates.dll
F:\! ASP.NET Now Writing test\AmiBigeye\AmiBigeye\bin\roslyn\System.Text.Encoding.CodePages.dll
F:\! ASP.NET Now Writing test\AmiBigeye\AmiBigeye\bin\roslyn\System.Threading.Tasks.Extensions.dll
F:\! ASP.NET Now Writing test\AmiBigeye\AmiBigeye\bin\roslyn\System.ValueTuple.dll
F:\! ASP.NET Now Writing test\AmiBigeye\AmiBigeye\bin\roslyn\System.Xml.ReaderWriter.dll
F:\! ASP.NET Now Writing test\AmiBigeye\AmiBigeye\bin\roslyn\System.Xml.XmlDocument.dll
F:\! ASP.NET Now Writing test\AmiBigeye\AmiBigeye\bin\roslyn\System.Xml.XPath.dll
F:\! ASP.NET Now Writing test\AmiBigeye\AmiBigeye\bin\roslyn\System.Xml.XPath.XDocument.dll
F:\! ASP.NET Now Writing test\AmiBigeye\AmiBigeye\bin\roslyn\vbc.exe
F:\! ASP.NET Now Writing test\AmiBigeye\AmiBigeye\bin\roslyn\vbc.exe.config
F:\! ASP.NET Now Writing test\AmiBigeye\AmiBigeye\bin\roslyn\vbc.rsp
F:\! ASP.NET Now Writing test\AmiBigeye\AmiBigeye\bin\roslyn\VBCSCompiler.exe
F:\! ASP.NET Now Writing test\AmiBigeye\AmiBigeye\bin\roslyn\VBCSCompiler.exe.config
F:\! ASP.NET Now Writing test\AmiBigeye\AmiBigeye\bin\Antlr3.Runtime.dll
F:\! ASP.NET Now Writing test\AmiBigeye\AmiBigeye\bin\AspNet.ScriptManager.jQuery.dll
F:\! ASP.NET Now Writing test\AmiBigeye\AmiBigeye\bin\Microsoft.AspNet.FriendlyUrls.dll
F:\! ASP.NET Now Writing test\AmiBigeye\AmiBigeye\bin\Microsoft.AspNet.Web.Optimization.WebForms.dll
F:\! ASP.NET Now Writing test\AmiBigeye\AmiBigeye\bin\Microsoft.CodeDom.Providers.DotNetCompilerPlatform.dll
F:\! ASP.NET Now Writing test\AmiBigeye\AmiBigeye\bin\Microsoft.ScriptManager.MSAjax.dll
F:\! ASP.NET Now Writing test\AmiBigeye\AmiBigeye\bin\Microsoft.ScriptManager.WebForms.dll
F:\! ASP.NET Now Writing test\AmiBigeye\AmiBigeye\bin\Microsoft.Web.Infrastructure.dll
F:\! ASP.NET Now Writing test\AmiBigeye\AmiBigeye\bin\Newtonsoft.Json.dll
F:\! ASP.NET Now Writing test\AmiBigeye\AmiBigeye\bin\System.Web.Optimization.dll
F:\! ASP.NET Now Writing test\AmiBigeye\AmiBigeye\bin\WebGrease.dll
F:\! ASP.NET Now Writing test\AmiBigeye\AmiBigeye\bin\System.Web.Optimization.xml
F:\! ASP.NET Now Writing test\AmiBigeye\AmiBigeye\bin\Newtonsoft.Json.xml
F:\! ASP.NET Now Writing test\AmiBigeye\AmiBigeye\bin\Antlr3.Runtime.pdb
F:\! ASP.NET Now Writing test\AmiBigeye\AmiBigeye\bin\Microsoft.AspNet.FriendlyUrls.xml
F:\! ASP.NET Now Writing test\AmiBigeye\AmiBigeye\bin\Microsoft.CodeDom.Providers.DotNetCompilerPlatform.xml
F:\! ASP.NET Now Writing test\AmiBigeye\AmiBigeye\obj\Debug\AmiBigeye.vbproj.AssemblyReference.cache
F:\! ASP.NET Now Writing test\AmiBigeye\AmiBigeye\obj\Debug\AmiBigeye.Resources.resources
F:\! ASP.NET Now Writing test\AmiBigeye\AmiBigeye\obj\Debug\AmiBigeye.vbproj.GenerateResource.cache
F:\! ASP.NET Now Writing test\AmiBigeye\AmiBigeye\obj\Debug\AmiBigeye.vbproj.CoreCompileInputs.cache
F:\! ASP.NET Now Writing test\AmiBigeye\AmiBigeye\obj\Debug\AmiBigeye.vbproj.CopyComplete
F:\! ASP.NET Now Writing test\AmiBigeye\AmiBigeye\obj\Debug\AmiBigeye.dll
F:\! ASP.NET Now Writing test\AmiBigeye\AmiBigeye\obj\Debug\WebForms_Basic.xml
F:\! ASP.NET Now Writing test\AmiBigeye\AmiBigeye\obj\Debug\AmiBigeye.pdb
D:\! ASP.NET Now Writing test\AmiBigeye\AmiBigeye\bin\AmiBigeye.dll.config
D:\! ASP.NET Now Writing test\AmiBigeye\AmiBigeye\bin\AmiBigeye.dll
D:\! ASP.NET Now Writing test\AmiBigeye\AmiBigeye\bin\AmiBigeye.pdb
D:\! ASP.NET Now Writing test\AmiBigeye\AmiBigeye\bin\WebForms_Basic.xml
D:\! ASP.NET Now Writing test\AmiBigeye\AmiBigeye\bin\roslyn\csc.exe
D:\! ASP.NET Now Writing test\AmiBigeye\AmiBigeye\bin\roslyn\csc.exe.config
D:\! ASP.NET Now Writing test\AmiBigeye\AmiBigeye\bin\roslyn\csc.rsp
D:\! ASP.NET Now Writing test\AmiBigeye\AmiBigeye\bin\roslyn\csi.exe
D:\! ASP.NET Now Writing test\AmiBigeye\AmiBigeye\bin\roslyn\csi.exe.config
D:\! ASP.NET Now Writing test\AmiBigeye\AmiBigeye\bin\roslyn\csi.rsp
D:\! ASP.NET Now Writing test\AmiBigeye\AmiBigeye\bin\roslyn\Microsoft.Build.Tasks.CodeAnalysis.dll
D:\! ASP.NET Now Writing test\AmiBigeye\AmiBigeye\bin\roslyn\Microsoft.CodeAnalysis.CSharp.dll
D:\! ASP.NET Now Writing test\AmiBigeye\AmiBigeye\bin\roslyn\Microsoft.CodeAnalysis.CSharp.Scripting.dll
D:\! ASP.NET Now Writing test\AmiBigeye\AmiBigeye\bin\roslyn\Microsoft.CodeAnalysis.dll
D:\! ASP.NET Now Writing test\AmiBigeye\AmiBigeye\bin\roslyn\Microsoft.CodeAnalysis.Scripting.dll
D:\! ASP.NET Now Writing test\AmiBigeye\AmiBigeye\bin\roslyn\Microsoft.CodeAnalysis.VisualBasic.dll
D:\! ASP.NET Now Writing test\AmiBigeye\AmiBigeye\bin\roslyn\Microsoft.CSharp.Core.targets
D:\! ASP.NET Now Writing test\AmiBigeye\AmiBigeye\bin\roslyn\Microsoft.DiaSymReader.Native.amd64.dll
D:\! ASP.NET Now Writing test\AmiBigeye\AmiBigeye\bin\roslyn\Microsoft.DiaSymReader.Native.x86.dll
D:\! ASP.NET Now Writing test\AmiBigeye\AmiBigeye\bin\roslyn\Microsoft.Managed.Core.targets
D:\! ASP.NET Now Writing test\AmiBigeye\AmiBigeye\bin\roslyn\Microsoft.VisualBasic.Core.targets
D:\! ASP.NET Now Writing test\AmiBigeye\AmiBigeye\bin\roslyn\Microsoft.Win32.Primitives.dll
D:\! ASP.NET Now Writing test\AmiBigeye\AmiBigeye\bin\roslyn\System.AppContext.dll
D:\! ASP.NET Now Writing test\AmiBigeye\AmiBigeye\bin\roslyn\System.Collections.Immutable.dll
D:\! ASP.NET Now Writing test\AmiBigeye\AmiBigeye\bin\roslyn\System.Console.dll
D:\! ASP.NET Now Writing test\AmiBigeye\AmiBigeye\bin\roslyn\System.Diagnostics.DiagnosticSource.dll
D:\! ASP.NET Now Writing test\AmiBigeye\AmiBigeye\bin\roslyn\System.Diagnostics.FileVersionInfo.dll
D:\! ASP.NET Now Writing test\AmiBigeye\AmiBigeye\bin\roslyn\System.Diagnostics.StackTrace.dll
D:\! ASP.NET Now Writing test\AmiBigeye\AmiBigeye\bin\roslyn\System.Globalization.Calendars.dll
D:\! ASP.NET Now Writing test\AmiBigeye\AmiBigeye\bin\roslyn\System.IO.Compression.dll
D:\! ASP.NET Now Writing test\AmiBigeye\AmiBigeye\bin\roslyn\System.IO.Compression.ZipFile.dll
D:\! ASP.NET Now Writing test\AmiBigeye\AmiBigeye\bin\roslyn\System.IO.FileSystem.dll
D:\! ASP.NET Now Writing test\AmiBigeye\AmiBigeye\bin\roslyn\System.IO.FileSystem.Primitives.dll
D:\! ASP.NET Now Writing test\AmiBigeye\AmiBigeye\bin\roslyn\System.Net.Http.dll
D:\! ASP.NET Now Writing test\AmiBigeye\AmiBigeye\bin\roslyn\System.Net.Sockets.dll
D:\! ASP.NET Now Writing test\AmiBigeye\AmiBigeye\bin\roslyn\System.Reflection.Metadata.dll
D:\! ASP.NET Now Writing test\AmiBigeye\AmiBigeye\bin\roslyn\System.Runtime.InteropServices.RuntimeInformation.dll
D:\! ASP.NET Now Writing test\AmiBigeye\AmiBigeye\bin\roslyn\System.Security.Cryptography.Algorithms.dll
D:\! ASP.NET Now Writing test\AmiBigeye\AmiBigeye\bin\roslyn\System.Security.Cryptography.Encoding.dll
D:\! ASP.NET Now Writing test\AmiBigeye\AmiBigeye\bin\roslyn\System.Security.Cryptography.Primitives.dll
D:\! ASP.NET Now Writing test\AmiBigeye\AmiBigeye\bin\roslyn\System.Security.Cryptography.X509Certificates.dll
D:\! ASP.NET Now Writing test\AmiBigeye\AmiBigeye\bin\roslyn\System.Text.Encoding.CodePages.dll
D:\! ASP.NET Now Writing test\AmiBigeye\AmiBigeye\bin\roslyn\System.Threading.Tasks.Extensions.dll
D:\! ASP.NET Now Writing test\AmiBigeye\AmiBigeye\bin\roslyn\System.ValueTuple.dll
D:\! ASP.NET Now Writing test\AmiBigeye\AmiBigeye\bin\roslyn\System.Xml.ReaderWriter.dll
D:\! ASP.NET Now Writing test\AmiBigeye\AmiBigeye\bin\roslyn\System.Xml.XmlDocument.dll
D:\! ASP.NET Now Writing test\AmiBigeye\AmiBigeye\bin\roslyn\System.Xml.XPath.dll
D:\! ASP.NET Now Writing test\AmiBigeye\AmiBigeye\bin\roslyn\System.Xml.XPath.XDocument.dll
D:\! ASP.NET Now Writing test\AmiBigeye\AmiBigeye\bin\roslyn\vbc.exe
D:\! ASP.NET Now Writing test\AmiBigeye\AmiBigeye\bin\roslyn\vbc.exe.config
D:\! ASP.NET Now Writing test\AmiBigeye\AmiBigeye\bin\roslyn\vbc.rsp
D:\! ASP.NET Now Writing test\AmiBigeye\AmiBigeye\bin\roslyn\VBCSCompiler.exe
D:\! ASP.NET Now Writing test\AmiBigeye\AmiBigeye\bin\roslyn\VBCSCompiler.exe.config
D:\! ASP.NET Now Writing test\AmiBigeye\AmiBigeye\bin\Antlr3.Runtime.dll
D:\! ASP.NET Now Writing test\AmiBigeye\AmiBigeye\bin\AspNet.ScriptManager.jQuery.dll
D:\! ASP.NET Now Writing test\AmiBigeye\AmiBigeye\bin\Microsoft.AspNet.FriendlyUrls.dll
D:\! ASP.NET Now Writing test\AmiBigeye\AmiBigeye\bin\Microsoft.AspNet.Web.Optimization.WebForms.dll
D:\! ASP.NET Now Writing test\AmiBigeye\AmiBigeye\bin\Microsoft.CodeDom.Providers.DotNetCompilerPlatform.dll
D:\! ASP.NET Now Writing test\AmiBigeye\AmiBigeye\bin\Microsoft.ScriptManager.MSAjax.dll
D:\! ASP.NET Now Writing test\AmiBigeye\AmiBigeye\bin\Microsoft.ScriptManager.WebForms.dll
D:\! ASP.NET Now Writing test\AmiBigeye\AmiBigeye\bin\Microsoft.Web.Infrastructure.dll
D:\! ASP.NET Now Writing test\AmiBigeye\AmiBigeye\bin\Newtonsoft.Json.dll
D:\! ASP.NET Now Writing test\AmiBigeye\AmiBigeye\bin\System.Web.Optimization.dll
D:\! ASP.NET Now Writing test\AmiBigeye\AmiBigeye\bin\WebGrease.dll
D:\! ASP.NET Now Writing test\AmiBigeye\AmiBigeye\bin\System.Web.Optimization.xml
D:\! ASP.NET Now Writing test\AmiBigeye\AmiBigeye\bin\Newtonsoft.Json.xml
D:\! ASP.NET Now Writing test\AmiBigeye\AmiBigeye\bin\Antlr3.Runtime.pdb
D:\! ASP.NET Now Writing test\AmiBigeye\AmiBigeye\bin\Microsoft.AspNet.FriendlyUrls.xml
D:\! ASP.NET Now Writing test\AmiBigeye\AmiBigeye\bin\Microsoft.CodeDom.Providers.DotNetCompilerPlatform.xml
D:\! ASP.NET Now Writing test\AmiBigeye\AmiBigeye\obj\Debug\AmiBigeye.vbproj.AssemblyReference.cache
D:\! ASP.NET Now Writing test\AmiBigeye\AmiBigeye\obj\Debug\AmiBigeye.Resources.resources
D:\! ASP.NET Now Writing test\AmiBigeye\AmiBigeye\obj\Debug\AmiBigeye.vbproj.GenerateResource.cache
D:\! ASP.NET Now Writing test\AmiBigeye\AmiBigeye\obj\Debug\AmiBigeye.vbproj.CoreCompileInputs.cache
D:\! ASP.NET Now Writing test\AmiBigeye\AmiBigeye\obj\Debug\AmiBigeye.vbproj.CopyComplete
D:\! ASP.NET Now Writing test\AmiBigeye\AmiBigeye\obj\Debug\AmiBigeye.dll
D:\! ASP.NET Now Writing test\AmiBigeye\AmiBigeye\obj\Debug\WebForms_Basic.xml
D:\! ASP.NET Now Writing test\AmiBigeye\AmiBigeye\obj\Debug\AmiBigeye.pdb
A:\AmiBigeye\bin\AmiBigeye.dll.config
A:\AmiBigeye\bin\AmiBigeye.dll
A:\AmiBigeye\bin\AmiBigeye.pdb
A:\AmiBigeye\bin\WebForms_Basic.xml
A:\AmiBigeye\bin\roslyn\csc.exe
A:\AmiBigeye\bin\roslyn\csc.exe.config
A:\AmiBigeye\bin\roslyn\csc.rsp
A:\AmiBigeye\bin\roslyn\csi.exe
A:\AmiBigeye\bin\roslyn\csi.exe.config
A:\AmiBigeye\bin\roslyn\csi.rsp
A:\AmiBigeye\bin\roslyn\Microsoft.Build.Tasks.CodeAnalysis.dll
A:\AmiBigeye\bin\roslyn\Microsoft.CodeAnalysis.CSharp.dll
A:\AmiBigeye\bin\roslyn\Microsoft.CodeAnalysis.CSharp.Scripting.dll
A:\AmiBigeye\bin\roslyn\Microsoft.CodeAnalysis.dll
A:\AmiBigeye\bin\roslyn\Microsoft.CodeAnalysis.Scripting.dll
A:\AmiBigeye\bin\roslyn\Microsoft.CodeAnalysis.VisualBasic.dll
A:\AmiBigeye\bin\roslyn\Microsoft.CSharp.Core.targets
A:\AmiBigeye\bin\roslyn\Microsoft.DiaSymReader.Native.amd64.dll
A:\AmiBigeye\bin\roslyn\Microsoft.DiaSymReader.Native.x86.dll
A:\AmiBigeye\bin\roslyn\Microsoft.Managed.Core.targets
A:\AmiBigeye\bin\roslyn\Microsoft.VisualBasic.Core.targets
A:\AmiBigeye\bin\roslyn\Microsoft.Win32.Primitives.dll
A:\AmiBigeye\bin\roslyn\System.AppContext.dll
A:\AmiBigeye\bin\roslyn\System.Collections.Immutable.dll
A:\AmiBigeye\bin\roslyn\System.Console.dll
A:\AmiBigeye\bin\roslyn\System.Diagnostics.DiagnosticSource.dll
A:\AmiBigeye\bin\roslyn\System.Diagnostics.FileVersionInfo.dll
A:\AmiBigeye\bin\roslyn\System.Diagnostics.StackTrace.dll
A:\AmiBigeye\bin\roslyn\System.Globalization.Calendars.dll
A:\AmiBigeye\bin\roslyn\System.IO.Compression.dll
A:\AmiBigeye\bin\roslyn\System.IO.Compression.ZipFile.dll
A:\AmiBigeye\bin\roslyn\System.IO.FileSystem.dll
A:\AmiBigeye\bin\roslyn\System.IO.FileSystem.Primitives.dll
A:\AmiBigeye\bin\roslyn\System.Net.Http.dll
A:\AmiBigeye\bin\roslyn\System.Net.Sockets.dll
A:\AmiBigeye\bin\roslyn\System.Reflection.Metadata.dll
A:\AmiBigeye\bin\roslyn\System.Runtime.InteropServices.RuntimeInformation.dll
A:\AmiBigeye\bin\roslyn\System.Security.Cryptography.Algorithms.dll
A:\AmiBigeye\bin\roslyn\System.Security.Cryptography.Encoding.dll
A:\AmiBigeye\bin\roslyn\System.Security.Cryptography.Primitives.dll
A:\AmiBigeye\bin\roslyn\System.Security.Cryptography.X509Certificates.dll
A:\AmiBigeye\bin\roslyn\System.Text.Encoding.CodePages.dll
A:\AmiBigeye\bin\roslyn\System.Threading.Tasks.Extensions.dll
A:\AmiBigeye\bin\roslyn\System.ValueTuple.dll
A:\AmiBigeye\bin\roslyn\System.Xml.ReaderWriter.dll
A:\AmiBigeye\bin\roslyn\System.Xml.XmlDocument.dll
A:\AmiBigeye\bin\roslyn\System.Xml.XPath.dll
A:\AmiBigeye\bin\roslyn\System.Xml.XPath.XDocument.dll
A:\AmiBigeye\bin\roslyn\vbc.exe
A:\AmiBigeye\bin\roslyn\vbc.exe.config
A:\AmiBigeye\bin\roslyn\vbc.rsp
A:\AmiBigeye\bin\roslyn\VBCSCompiler.exe
A:\AmiBigeye\bin\roslyn\VBCSCompiler.exe.config
A:\AmiBigeye\bin\Antlr3.Runtime.dll
A:\AmiBigeye\bin\AspNet.ScriptManager.jQuery.dll
A:\AmiBigeye\bin\Microsoft.AspNet.FriendlyUrls.dll
A:\AmiBigeye\bin\Microsoft.AspNet.Web.Optimization.WebForms.dll
A:\AmiBigeye\bin\Microsoft.CodeDom.Providers.DotNetCompilerPlatform.dll
A:\AmiBigeye\bin\Microsoft.ScriptManager.MSAjax.dll
A:\AmiBigeye\bin\Microsoft.ScriptManager.WebForms.dll
A:\AmiBigeye\bin\Microsoft.Web.Infrastructure.dll
A:\AmiBigeye\bin\Newtonsoft.Json.dll
A:\AmiBigeye\bin\System.Web.Optimization.dll
A:\AmiBigeye\bin\WebGrease.dll
A:\AmiBigeye\bin\System.Web.Optimization.xml
A:\AmiBigeye\bin\Newtonsoft.Json.xml
A:\AmiBigeye\bin\Antlr3.Runtime.pdb
A:\AmiBigeye\bin\Microsoft.AspNet.FriendlyUrls.xml
A:\AmiBigeye\bin\Microsoft.CodeDom.Providers.DotNetCompilerPlatform.xml
A:\AmiBigeye\obj\Debug\AmiBigeye.vbprojAssemblyReference.cache
A:\AmiBigeye\obj\Debug\AmiBigeye.Resources.resources
A:\AmiBigeye\obj\Debug\AmiBigeye.vbproj.GenerateResource.cache
A:\AmiBigeye\obj\Debug\AmiBigeye.vbproj.CoreCompileInputs.cache
A:\AmiBigeye\obj\Debug\AmiBigeye.vbproj.CopyComplete
A:\AmiBigeye\obj\Debug\AmiBigeye.dll
A:\AmiBigeye\obj\Debug\WebForms_Basic.xml
A:\AmiBigeye\obj\Debug\AmiBigeye.pdb
D:\! ASP.NET Now Writing test\AmiBigeye\AmiBigeye\AmiBigeye\bin\AmiBigeye.dll.config
D:\! ASP.NET Now Writing test\AmiBigeye\AmiBigeye\AmiBigeye\bin\AmiBigeye.dll
D:\! ASP.NET Now Writing test\AmiBigeye\AmiBigeye\AmiBigeye\bin\AmiBigeye.pdb
D:\! ASP.NET Now Writing test\AmiBigeye\AmiBigeye\AmiBigeye\bin\WebForms_Basic.xml
D:\! ASP.NET Now Writing test\AmiBigeye\AmiBigeye\AmiBigeye\bin\roslyn\csc.exe
D:\! ASP.NET Now Writing test\AmiBigeye\AmiBigeye\AmiBigeye\bin\roslyn\csc.exe.config
D:\! ASP.NET Now Writing test\AmiBigeye\AmiBigeye\AmiBigeye\bin\roslyn\csc.rsp
D:\! ASP.NET Now Writing test\AmiBigeye\AmiBigeye\AmiBigeye\bin\roslyn\csi.exe
D:\! ASP.NET Now Writing test\AmiBigeye\AmiBigeye\AmiBigeye\bin\roslyn\csi.exe.config
D:\! ASP.NET Now Writing test\AmiBigeye\AmiBigeye\AmiBigeye\bin\roslyn\csi.rsp
D:\! ASP.NET Now Writing test\AmiBigeye\AmiBigeye\AmiBigeye\bin\roslyn\Microsoft.Build.Tasks.CodeAnalysis.dll
D:\! ASP.NET Now Writing test\AmiBigeye\AmiBigeye\AmiBigeye\bin\roslyn\Microsoft.CodeAnalysis.CSharp.dll
D:\! ASP.NET Now Writing test\AmiBigeye\AmiBigeye\AmiBigeye\bin\roslyn\Microsoft.CodeAnalysis.CSharp.Scripting.dll
D:\! ASP.NET Now Writing test\AmiBigeye\AmiBigeye\AmiBigeye\bin\roslyn\Microsoft.CodeAnalysis.dll
D:\! ASP.NET Now Writing test\AmiBigeye\AmiBigeye\AmiBigeye\bin\roslyn\Microsoft.CodeAnalysis.Scripting.dll
D:\! ASP.NET Now Writing test\AmiBigeye\AmiBigeye\AmiBigeye\bin\roslyn\Microsoft.CodeAnalysis.VisualBasic.dll
D:\! ASP.NET Now Writing test\AmiBigeye\AmiBigeye\AmiBigeye\bin\roslyn\Microsoft.CSharp.Core.targets
D:\! ASP.NET Now Writing test\AmiBigeye\AmiBigeye\AmiBigeye\bin\roslyn\Microsoft.DiaSymReader.Native.amd64.dll
D:\! ASP.NET Now Writing test\AmiBigeye\AmiBigeye\AmiBigeye\bin\roslyn\Microsoft.DiaSymReader.Native.x86.dll
D:\! ASP.NET Now Writing test\AmiBigeye\AmiBigeye\AmiBigeye\bin\roslyn\Microsoft.Managed.Core.targets
D:\! ASP.NET Now Writing test\AmiBigeye\AmiBigeye\AmiBigeye\bin\roslyn\Microsoft.VisualBasic.Core.targets
D:\! ASP.NET Now Writing test\AmiBigeye\AmiBigeye\AmiBigeye\bin\roslyn\Microsoft.Win32.Primitives.dll
D:\! ASP.NET Now Writing test\AmiBigeye\AmiBigeye\AmiBigeye\bin\roslyn\System.AppContext.dll
D:\! ASP.NET Now Writing test\AmiBigeye\AmiBigeye\AmiBigeye\bin\roslyn\System.Collections.Immutable.dll
D:\! ASP.NET Now Writing test\AmiBigeye\AmiBigeye\AmiBigeye\bin\roslyn\System.Console.dll
D:\! ASP.NET Now Writing test\AmiBigeye\AmiBigeye\AmiBigeye\bin\roslyn\System.Diagnostics.DiagnosticSource.dll
D:\! ASP.NET Now Writing test\AmiBigeye\AmiBigeye\AmiBigeye\bin\roslyn\System.Diagnostics.FileVersionInfo.dll
D:\! ASP.NET Now Writing test\AmiBigeye\AmiBigeye\AmiBigeye\bin\roslyn\System.Diagnostics.StackTrace.dll
D:\! ASP.NET Now Writing test\AmiBigeye\AmiBigeye\AmiBigeye\bin\roslyn\System.Globalization.Calendars.dll
D:\! ASP.NET Now Writing test\AmiBigeye\AmiBigeye\AmiBigeye\bin\roslyn\System.IO.Compression.dll
D:\! ASP.NET Now Writing test\AmiBigeye\AmiBigeye\AmiBigeye\bin\roslyn\System.IO.Compression.ZipFile.dll
D:\! ASP.NET Now Writing test\AmiBigeye\AmiBigeye\AmiBigeye\bin\roslyn\System.IO.FileSystem.dll
D:\! ASP.NET Now Writing test\AmiBigeye\AmiBigeye\AmiBigeye\bin\roslyn\System.IO.FileSystem.Primitives.dll
D:\! ASP.NET Now Writing test\AmiBigeye\AmiBigeye\AmiBigeye\bin\roslyn\System.Net.Http.dll
D:\! ASP.NET Now Writing test\AmiBigeye\AmiBigeye\AmiBigeye\bin\roslyn\System.Net.Sockets.dll
D:\! ASP.NET Now Writing test\AmiBigeye\AmiBigeye\AmiBigeye\bin\roslyn\System.Reflection.Metadata.dll
D:\! ASP.NET Now Writing test\AmiBigeye\AmiBigeye\AmiBigeye\bin\roslyn\System.Runtime.InteropServices.RuntimeInformation.dll
D:\! ASP.NET Now Writing test\AmiBigeye\AmiBigeye\AmiBigeye\bin\roslyn\System.Security.Cryptography.Algorithms.dll
D:\! ASP.NET Now Writing test\AmiBigeye\AmiBigeye\AmiBigeye\bin\roslyn\System.Security.Cryptography.Encoding.dll
D:\! ASP.NET Now Writing test\AmiBigeye\AmiBigeye\AmiBigeye\bin\roslyn\System.Security.Cryptography.Primitives.dll
D:\! ASP.NET Now Writing test\AmiBigeye\AmiBigeye\AmiBigeye\bin\roslyn\System.Security.Cryptography.X509Certificates.dll
D:\! ASP.NET Now Writing test\AmiBigeye\AmiBigeye\AmiBigeye\bin\roslyn\System.Text.Encoding.CodePages.dll
D:\! ASP.NET Now Writing test\AmiBigeye\AmiBigeye\AmiBigeye\bin\roslyn\System.Threading.Tasks.Extensions.dll
D:\! ASP.NET Now Writing test\AmiBigeye\AmiBigeye\AmiBigeye\bin\roslyn\System.ValueTuple.dll
D:\! ASP.NET Now Writing test\AmiBigeye\AmiBigeye\AmiBigeye\bin\roslyn\System.Xml.ReaderWriter.dll
D:\! ASP.NET Now Writing test\AmiBigeye\AmiBigeye\AmiBigeye\bin\roslyn\System.Xml.XmlDocument.dll
D:\! ASP.NET Now Writing test\AmiBigeye\AmiBigeye\AmiBigeye\bin\roslyn\System.Xml.XPath.dll
D:\! ASP.NET Now Writing test\AmiBigeye\AmiBigeye\AmiBigeye\bin\roslyn\System.Xml.XPath.XDocument.dll
D:\! ASP.NET Now Writing test\AmiBigeye\AmiBigeye\AmiBigeye\bin\roslyn\vbc.exe
D:\! ASP.NET Now Writing test\AmiBigeye\AmiBigeye\AmiBigeye\bin\roslyn\vbc.exe.config
D:\! ASP.NET Now Writing test\AmiBigeye\AmiBigeye\AmiBigeye\bin\roslyn\vbc.rsp
D:\! ASP.NET Now Writing test\AmiBigeye\AmiBigeye\AmiBigeye\bin\roslyn\VBCSCompiler.exe
D:\! ASP.NET Now Writing test\AmiBigeye\AmiBigeye\AmiBigeye\bin\roslyn\VBCSCompiler.exe.config
D:\! ASP.NET Now Writing test\AmiBigeye\AmiBigeye\AmiBigeye\bin\Antlr3.Runtime.dll
D:\! ASP.NET Now Writing test\AmiBigeye\AmiBigeye\AmiBigeye\bin\AspNet.ScriptManager.jQuery.dll
D:\! ASP.NET Now Writing test\AmiBigeye\AmiBigeye\AmiBigeye\bin\Microsoft.AspNet.FriendlyUrls.dll
D:\! ASP.NET Now Writing test\AmiBigeye\AmiBigeye\AmiBigeye\bin\Microsoft.AspNet.Web.Optimization.WebForms.dll
D:\! ASP.NET Now Writing test\AmiBigeye\AmiBigeye\AmiBigeye\bin\Microsoft.CodeDom.Providers.DotNetCompilerPlatform.dll
D:\! ASP.NET Now Writing test\AmiBigeye\AmiBigeye\AmiBigeye\bin\Microsoft.ScriptManager.MSAjax.dll
D:\! ASP.NET Now Writing test\AmiBigeye\AmiBigeye\AmiBigeye\bin\Microsoft.ScriptManager.WebForms.dll
D:\! ASP.NET Now Writing test\AmiBigeye\AmiBigeye\AmiBigeye\bin\Microsoft.Web.Infrastructure.dll
D:\! ASP.NET Now Writing test\AmiBigeye\AmiBigeye\AmiBigeye\bin\Newtonsoft.Json.dll
D:\! ASP.NET Now Writing test\AmiBigeye\AmiBigeye\AmiBigeye\bin\System.Web.Optimization.dll
D:\! ASP.NET Now Writing test\AmiBigeye\AmiBigeye\AmiBigeye\bin\WebGrease.dll
D:\! ASP.NET Now Writing test\AmiBigeye\AmiBigeye\AmiBigeye\bin\System.Web.Optimization.xml
D:\! ASP.NET Now Writing test\AmiBigeye\AmiBigeye\AmiBigeye\bin\Newtonsoft.Json.xml
D:\! ASP.NET Now Writing test\AmiBigeye\AmiBigeye\AmiBigeye\bin\Antlr3.Runtime.pdb
D:\! ASP.NET Now Writing test\AmiBigeye\AmiBigeye\AmiBigeye\bin\Microsoft.AspNet.FriendlyUrls.xml
D:\! ASP.NET Now Writing test\AmiBigeye\AmiBigeye\AmiBigeye\bin\Microsoft.CodeDom.Providers.DotNetCompilerPlatform.xml
D:\! ASP.NET Now Writing test\AmiBigeye\AmiBigeye\AmiBigeye\obj\Debug\AmiBigeye.vbprojAssemblyReference.cache
D:\! ASP.NET Now Writing test\AmiBigeye\AmiBigeye\AmiBigeye\obj\Debug\AmiBigeye.Resources.resources
D:\! ASP.NET Now Writing test\AmiBigeye\AmiBigeye\AmiBigeye\obj\Debug\AmiBigeye.vbproj.GenerateResource.cache
D:\! ASP.NET Now Writing test\AmiBigeye\AmiBigeye\AmiBigeye\obj\Debug\AmiBigeye.vbproj.CoreCompileInputs.cache
D:\! ASP.NET Now Writing test\AmiBigeye\AmiBigeye\AmiBigeye\obj\Debug\AmiBigeye.vbproj.CopyComplete
D:\! ASP.NET Now Writing test\AmiBigeye\AmiBigeye\AmiBigeye\obj\Debug\AmiBigeye.dll
D:\! ASP.NET Now Writing test\AmiBigeye\AmiBigeye\AmiBigeye\obj\Debug\WebForms_Basic.xml
D:\! ASP.NET Now Writing test\AmiBigeye\AmiBigeye\AmiBigeye\obj\Debug\AmiBigeye.pdb
C:\dev\AmiBigeye\AmiBigeye\bin\AmiBigeye.dll.config
C:\dev\AmiBigeye\AmiBigeye\bin\AmiBigeye.dll
C:\dev\AmiBigeye\AmiBigeye\bin\AmiBigeye.pdb
C:\dev\AmiBigeye\AmiBigeye\bin\WebForms_Basic.xml
C:\dev\AmiBigeye\AmiBigeye\bin\roslyn\csc.exe
C:\dev\AmiBigeye\AmiBigeye\bin\roslyn\csc.exe.config
C:\dev\AmiBigeye\AmiBigeye\bin\roslyn\csc.rsp
C:\dev\AmiBigeye\AmiBigeye\bin\roslyn\csi.exe
C:\dev\AmiBigeye\AmiBigeye\bin\roslyn\csi.exe.config
C:\dev\AmiBigeye\AmiBigeye\bin\roslyn\csi.rsp
C:\dev\AmiBigeye\AmiBigeye\bin\roslyn\Microsoft.Build.Tasks.CodeAnalysis.dll
C:\dev\AmiBigeye\AmiBigeye\bin\roslyn\Microsoft.CodeAnalysis.CSharp.dll
C:\dev\AmiBigeye\AmiBigeye\bin\roslyn\Microsoft.CodeAnalysis.CSharp.Scripting.dll
C:\dev\AmiBigeye\AmiBigeye\bin\roslyn\Microsoft.CodeAnalysis.dll
C:\dev\AmiBigeye\AmiBigeye\bin\roslyn\Microsoft.CodeAnalysis.Scripting.dll
C:\dev\AmiBigeye\AmiBigeye\bin\roslyn\Microsoft.CodeAnalysis.VisualBasic.dll
C:\dev\AmiBigeye\AmiBigeye\bin\roslyn\Microsoft.CSharp.Core.targets
C:\dev\AmiBigeye\AmiBigeye\bin\roslyn\Microsoft.DiaSymReader.Native.amd64.dll
C:\dev\AmiBigeye\AmiBigeye\bin\roslyn\Microsoft.DiaSymReader.Native.x86.dll
C:\dev\AmiBigeye\AmiBigeye\bin\roslyn\Microsoft.Managed.Core.targets
C:\dev\AmiBigeye\AmiBigeye\bin\roslyn\Microsoft.VisualBasic.Core.targets
C:\dev\AmiBigeye\AmiBigeye\bin\roslyn\Microsoft.Win32.Primitives.dll
C:\dev\AmiBigeye\AmiBigeye\bin\roslyn\System.AppContext.dll
C:\dev\AmiBigeye\AmiBigeye\bin\roslyn\System.Collections.Immutable.dll
C:\dev\AmiBigeye\AmiBigeye\bin\roslyn\System.Console.dll
C:\dev\AmiBigeye\AmiBigeye\bin\roslyn\System.Diagnostics.DiagnosticSource.dll
C:\dev\AmiBigeye\AmiBigeye\bin\roslyn\System.Diagnostics.FileVersionInfo.dll
C:\dev\AmiBigeye\AmiBigeye\bin\roslyn\System.Diagnostics.StackTrace.dll
C:\dev\AmiBigeye\AmiBigeye\bin\roslyn\System.Globalization.Calendars.dll
C:\dev\AmiBigeye\AmiBigeye\bin\roslyn\System.IO.Compression.dll
C:\dev\AmiBigeye\AmiBigeye\bin\roslyn\System.IO.Compression.ZipFile.dll
C:\dev\AmiBigeye\AmiBigeye\bin\roslyn\System.IO.FileSystem.dll
C:\dev\AmiBigeye\AmiBigeye\bin\roslyn\System.IO.FileSystem.Primitives.dll
C:\dev\AmiBigeye\AmiBigeye\bin\roslyn\System.Net.Http.dll
C:\dev\AmiBigeye\AmiBigeye\bin\roslyn\System.Net.Sockets.dll
C:\dev\AmiBigeye\AmiBigeye\bin\roslyn\System.Reflection.Metadata.dll
C:\dev\AmiBigeye\AmiBigeye\bin\roslyn\System.Runtime.InteropServices.RuntimeInformation.dll
C:\dev\AmiBigeye\AmiBigeye\bin\roslyn\System.Security.Cryptography.Algorithms.dll
C:\dev\AmiBigeye\AmiBigeye\bin\roslyn\System.Security.Cryptography.Encoding.dll
C:\dev\AmiBigeye\AmiBigeye\bin\roslyn\System.Security.Cryptography.Primitives.dll
C:\dev\AmiBigeye\AmiBigeye\bin\roslyn\System.Security.Cryptography.X509Certificates.dll
C:\dev\AmiBigeye\AmiBigeye\bin\roslyn\System.Text.Encoding.CodePages.dll
C:\dev\AmiBigeye\AmiBigeye\bin\roslyn\System.Threading.Tasks.Extensions.dll
C:\dev\AmiBigeye\AmiBigeye\bin\roslyn\System.ValueTuple.dll
C:\dev\AmiBigeye\AmiBigeye\bin\roslyn\System.Xml.ReaderWriter.dll
C:\dev\AmiBigeye\AmiBigeye\bin\roslyn\System.Xml.XmlDocument.dll
C:\dev\AmiBigeye\AmiBigeye\bin\roslyn\System.Xml.XPath.dll
C:\dev\AmiBigeye\AmiBigeye\bin\roslyn\System.Xml.XPath.XDocument.dll
C:\dev\AmiBigeye\AmiBigeye\bin\roslyn\vbc.exe
C:\dev\AmiBigeye\AmiBigeye\bin\roslyn\vbc.exe.config
C:\dev\AmiBigeye\AmiBigeye\bin\roslyn\vbc.rsp
C:\dev\AmiBigeye\AmiBigeye\bin\roslyn\VBCSCompiler.exe
C:\dev\AmiBigeye\AmiBigeye\bin\roslyn\VBCSCompiler.exe.config
C:\dev\AmiBigeye\AmiBigeye\bin\Antlr3.Runtime.dll
C:\dev\AmiBigeye\AmiBigeye\bin\AspNet.ScriptManager.jQuery.dll
C:\dev\AmiBigeye\AmiBigeye\bin\Microsoft.AspNet.FriendlyUrls.dll
C:\dev\AmiBigeye\AmiBigeye\bin\Microsoft.AspNet.Web.Optimization.WebForms.dll
C:\dev\AmiBigeye\AmiBigeye\bin\Microsoft.CodeDom.Providers.DotNetCompilerPlatform.dll
C:\dev\AmiBigeye\AmiBigeye\bin\Microsoft.ScriptManager.MSAjax.dll
C:\dev\AmiBigeye\AmiBigeye\bin\Microsoft.ScriptManager.WebForms.dll
C:\dev\AmiBigeye\AmiBigeye\bin\Microsoft.Web.Infrastructure.dll
C:\dev\AmiBigeye\AmiBigeye\bin\Newtonsoft.Json.dll
C:\dev\AmiBigeye\AmiBigeye\bin\System.Web.Optimization.dll
C:\dev\AmiBigeye\AmiBigeye\bin\WebGrease.dll
C:\dev\AmiBigeye\AmiBigeye\bin\System.Web.Optimization.xml
C:\dev\AmiBigeye\AmiBigeye\bin\Newtonsoft.Json.xml
C:\dev\AmiBigeye\AmiBigeye\bin\Antlr3.Runtime.pdb
C:\dev\AmiBigeye\AmiBigeye\bin\Microsoft.AspNet.FriendlyUrls.xml
C:\dev\AmiBigeye\AmiBigeye\bin\Microsoft.CodeDom.Providers.DotNetCompilerPlatform.xml
C:\dev\AmiBigeye\AmiBigeye\obj\Debug\AmiBigeye.vbproj.AssemblyReference.cache
C:\dev\AmiBigeye\AmiBigeye\obj\Debug\AmiBigeye.Resources.resources
C:\dev\AmiBigeye\AmiBigeye\obj\Debug\AmiBigeye.vbproj.GenerateResource.cache
C:\dev\AmiBigeye\AmiBigeye\obj\Debug\AmiBigeye.vbproj.CoreCompileInputs.cache
C:\dev\AmiBigeye\AmiBigeye\obj\Debug\AmiBigeye.vbproj.Up2Date
C:\dev\AmiBigeye\AmiBigeye\obj\Debug\AmiBigeye.dll
C:\dev\AmiBigeye\AmiBigeye\obj\Debug\WebForms_Basic.xml
C:\dev\AmiBigeye\AmiBigeye\obj\Debug\AmiBigeye.pdb
