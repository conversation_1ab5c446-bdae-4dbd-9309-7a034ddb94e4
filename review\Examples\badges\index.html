<!doctype html>
<html lang="en" data-bs-theme="auto">
  <head><script src="../assets/js/color-modes.js"></script>

    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="description" content="">
    <meta name="author" content="<PERSON>, <PERSON>, and Bootstrap contributors">
    <meta name="generator" content="Hugo 0.118.2">
    <title>Badges · Bootstrap v5.3</title>

    <link rel="canonical" href="https://getbootstrap.com/docs/5.3/examples/badges/">

    

    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/@docsearch/css@3">

<link href="../assets/dist/css/bootstrap.min.css" rel="stylesheet">

    <style>
      .bd-placeholder-img {
        font-size: 1.125rem;
        text-anchor: middle;
        -webkit-user-select: none;
        -moz-user-select: none;
        user-select: none;
      }

      @media (min-width: 768px) {
        .bd-placeholder-img-lg {
          font-size: 3.5rem;
        }
      }

      .b-example-divider {
        width: 100%;
        height: 3rem;
        background-color: rgba(0, 0, 0, .1);
        border: solid rgba(0, 0, 0, .15);
        border-width: 1px 0;
        box-shadow: inset 0 .5em 1.5em rgba(0, 0, 0, .1), inset 0 .125em .5em rgba(0, 0, 0, .15);
      }

      .b-example-vr {
        flex-shrink: 0;
        width: 1.5rem;
        height: 100vh;
      }

      .bi {
        vertical-align: -.125em;
        fill: currentColor;
      }

      .nav-scroller {
        position: relative;
        z-index: 2;
        height: 2.75rem;
        overflow-y: hidden;
      }

      .nav-scroller .nav {
        display: flex;
        flex-wrap: nowrap;
        padding-bottom: 1rem;
        margin-top: -1px;
        overflow-x: auto;
        text-align: center;
        white-space: nowrap;
        -webkit-overflow-scrolling: touch;
      }

      .btn-bd-primary {
        --bd-violet-bg: #712cf9;
        --bd-violet-rgb: 112.520718, 44.062154, 249.437846;

        --bs-btn-font-weight: 600;
        --bs-btn-color: var(--bs-white);
        --bs-btn-bg: var(--bd-violet-bg);
        --bs-btn-border-color: var(--bd-violet-bg);
        --bs-btn-hover-color: var(--bs-white);
        --bs-btn-hover-bg: #6528e0;
        --bs-btn-hover-border-color: #6528e0;
        --bs-btn-focus-shadow-rgb: var(--bd-violet-rgb);
        --bs-btn-active-color: var(--bs-btn-hover-color);
        --bs-btn-active-bg: #5a23c8;
        --bs-btn-active-border-color: #5a23c8;
      }

      .bd-mode-toggle {
        z-index: 1500;
      }

      .bd-mode-toggle .dropdown-menu .active .bi {
        display: block !important;
      }
    </style>

    
    <!-- Custom styles for this template -->
    <link href="badges.css" rel="stylesheet">
  </head>
  <body>
    <svg xmlns="http://www.w3.org/2000/svg" class="d-none">
      <symbol id="check2" viewBox="0 0 16 16">
        <path d="M13.854 3.646a.5.5 0 0 1 0 .708l-7 7a.5.5 0 0 1-.708 0l-3.5-3.5a.5.5 0 1 1 .708-.708L6.5 10.293l6.646-6.647a.5.5 0 0 1 .708 0z"/>
      </symbol>
      <symbol id="circle-half" viewBox="0 0 16 16">
        <path d="M8 15A7 7 0 1 0 8 1v14zm0 1A8 8 0 1 1 8 0a8 8 0 0 1 0 16z"/>
      </symbol>
      <symbol id="moon-stars-fill" viewBox="0 0 16 16">
        <path d="M6 .278a.768.768 0 0 1 .08.858 7.208 7.208 0 0 0-.878 3.46c0 4.021 3.278 7.277 7.318 7.277.527 0 1.04-.055 1.533-.16a.787.787 0 0 1 .81.316.733.733 0 0 1-.031.893A8.349 8.349 0 0 1 8.344 16C3.734 16 0 12.286 0 7.71 0 4.266 2.114 1.312 5.124.06A.752.752 0 0 1 6 .278z"/>
        <path d="M10.794 3.148a.217.217 0 0 1 .412 0l.387 1.162c.173.518.579.924 1.097 1.097l1.162.387a.217.217 0 0 1 0 .412l-1.162.387a1.734 1.734 0 0 0-1.097 1.097l-.387 1.162a.217.217 0 0 1-.412 0l-.387-1.162A1.734 1.734 0 0 0 9.31 6.593l-1.162-.387a.217.217 0 0 1 0-.412l1.162-.387a1.734 1.734 0 0 0 1.097-1.097l.387-1.162zM13.863.099a.145.145 0 0 1 .274 0l.258.774c.115.346.386.617.732.732l.774.258a.145.145 0 0 1 0 .274l-.774.258a1.156 1.156 0 0 0-.732.732l-.258.774a.145.145 0 0 1-.274 0l-.258-.774a1.156 1.156 0 0 0-.732-.732l-.774-.258a.145.145 0 0 1 0-.274l.774-.258c.346-.115.617-.386.732-.732L13.863.1z"/>
      </symbol>
      <symbol id="sun-fill" viewBox="0 0 16 16">
        <path d="M8 12a4 4 0 1 0 0-8 4 4 0 0 0 0 8zM8 0a.5.5 0 0 1 .5.5v2a.5.5 0 0 1-1 0v-2A.5.5 0 0 1 8 0zm0 13a.5.5 0 0 1 .5.5v2a.5.5 0 0 1-1 0v-2A.5.5 0 0 1 8 13zm8-5a.5.5 0 0 1-.5.5h-2a.5.5 0 0 1 0-1h2a.5.5 0 0 1 .5.5zM3 8a.5.5 0 0 1-.5.5h-2a.5.5 0 0 1 0-1h2A.5.5 0 0 1 3 8zm10.657-5.657a.5.5 0 0 1 0 .707l-1.414 1.415a.5.5 0 1 1-.707-.708l1.414-1.414a.5.5 0 0 1 .707 0zm-9.193 9.193a.5.5 0 0 1 0 .707L3.05 13.657a.5.5 0 0 1-.707-.707l1.414-1.414a.5.5 0 0 1 .707 0zm9.193 2.121a.5.5 0 0 1-.707 0l-1.414-1.414a.5.5 0 0 1 .707-.707l1.414 1.414a.5.5 0 0 1 0 .707zM4.464 4.465a.5.5 0 0 1-.707 0L2.343 3.05a.5.5 0 1 1 .707-.707l1.414 1.414a.5.5 0 0 1 0 .708z"/>
      </symbol>
    </svg>

    <div class="dropdown position-fixed bottom-0 end-0 mb-3 me-3 bd-mode-toggle">
      <button class="btn btn-bd-primary py-2 dropdown-toggle d-flex align-items-center"
              id="bd-theme"
              type="button"
              aria-expanded="false"
              data-bs-toggle="dropdown"
              aria-label="Toggle theme (auto)">
        <svg class="bi my-1 theme-icon-active" width="1em" height="1em"><use href="#circle-half"></use></svg>
        <span class="visually-hidden" id="bd-theme-text">Toggle theme</span>
      </button>
      <ul class="dropdown-menu dropdown-menu-end shadow" aria-labelledby="bd-theme-text">
        <li>
          <button type="button" class="dropdown-item d-flex align-items-center" data-bs-theme-value="light" aria-pressed="false">
            <svg class="bi me-2 opacity-50 theme-icon" width="1em" height="1em"><use href="#sun-fill"></use></svg>
            Light
            <svg class="bi ms-auto d-none" width="1em" height="1em"><use href="#check2"></use></svg>
          </button>
        </li>
        <li>
          <button type="button" class="dropdown-item d-flex align-items-center" data-bs-theme-value="dark" aria-pressed="false">
            <svg class="bi me-2 opacity-50 theme-icon" width="1em" height="1em"><use href="#moon-stars-fill"></use></svg>
            Dark
            <svg class="bi ms-auto d-none" width="1em" height="1em"><use href="#check2"></use></svg>
          </button>
        </li>
        <li>
          <button type="button" class="dropdown-item d-flex align-items-center active" data-bs-theme-value="auto" aria-pressed="true">
            <svg class="bi me-2 opacity-50 theme-icon" width="1em" height="1em"><use href="#circle-half"></use></svg>
            Auto
            <svg class="bi ms-auto d-none" width="1em" height="1em"><use href="#check2"></use></svg>
          </button>
        </li>
      </ul>
    </div>

    
<svg xmlns="http://www.w3.org/2000/svg" class="d-none">
  <symbol id="x-circle-fill" viewBox="0 0 16 16">
    <path d="M16 8A8 8 0 1 1 0 8a8 8 0 0 1 16 0zM5.354 4.646a.5.5 0 1 0-.708.708L7.293 8l-2.647 2.646a.5.5 0 0 0 .708.708L8 8.707l2.646 2.647a.5.5 0 0 0 .708-.708L8.707 8l2.647-2.646a.5.5 0 0 0-.708-.708L8 7.293 5.354 4.646z"/>
  </symbol>
</svg>

<div class="d-flex gap-2 justify-content-center py-5">
  <span class="badge text-bg-primary rounded-pill">Primary</span>
  <span class="badge text-bg-secondary rounded-pill">Secondary</span>
  <span class="badge text-bg-success rounded-pill">Success</span>
  <span class="badge text-bg-danger rounded-pill">Danger</span>
  <span class="badge text-bg-warning rounded-pill">Warning</span>
  <span class="badge text-bg-info rounded-pill">Info</span>
  <span class="badge text-bg-light rounded-pill">Light</span>
  <span class="badge text-bg-dark rounded-pill">Dark</span>
</div>

<div class="b-example-divider"></div>

<div class="d-flex gap-2 justify-content-center py-5">
  <span class="badge bg-primary-subtle text-primary-emphasis rounded-pill">Primary</span>
  <span class="badge bg-secondary-subtle text-secondary-emphasis rounded-pill">Secondary</span>
  <span class="badge bg-success-subtle text-success-emphasis rounded-pill">Success</span>
  <span class="badge bg-danger-subtle text-danger-emphasis rounded-pill">Danger</span>
  <span class="badge bg-warning-subtle text-warning-emphasis rounded-pill">Warning</span>
  <span class="badge bg-info-subtle text-info-emphasis rounded-pill">Info</span>
  <span class="badge bg-light-subtle text-light-emphasis rounded-pill">Light</span>
  <span class="badge bg-dark-subtle text-dark-emphasis rounded-pill">Dark</span>
</div>

<div class="b-example-divider"></div>

<div class="d-flex gap-2 justify-content-center py-5">
  <span class="badge bg-primary-subtle border border-primary-subtle text-primary-emphasis rounded-pill">Primary</span>
  <span class="badge bg-secondary-subtle border border-secondary-subtle text-secondary-emphasis rounded-pill">Secondary</span>
  <span class="badge bg-success-subtle border border-success-subtle text-success-emphasis rounded-pill">Success</span>
  <span class="badge bg-danger-subtle border border-danger-subtle text-danger-emphasis rounded-pill">Danger</span>
  <span class="badge bg-warning-subtle border border-warning-subtle text-warning-emphasis rounded-pill">Warning</span>
  <span class="badge bg-info-subtle border border-info-subtle text-info-emphasis rounded-pill">Info</span>
  <span class="badge bg-light-subtle border border-light-subtle text-light-emphasis rounded-pill">Light</span>
  <span class="badge bg-dark-subtle border border-dark-subtle text-dark-emphasis rounded-pill">Dark</span>
</div>

<div class="b-example-divider"></div>

<div class="d-flex gap-2 justify-content-center py-5">
  <span class="badge d-flex align-items-center p-1 pe-2 text-primary-emphasis bg-primary-subtle border border-primary-subtle rounded-pill">
    <img class="rounded-circle me-1" width="24" height="24" src="https://github.com/mdo.png" alt="">Primary
  </span>
  <span class="badge d-flex align-items-center p-1 pe-2 text-secondary-emphasis bg-secondary-subtle border border-secondary-subtle rounded-pill">
    <img class="rounded-circle me-1" width="24" height="24" src="https://github.com/mdo.png" alt="">Secondary
  </span>
  <span class="badge d-flex align-items-center p-1 pe-2 text-success-emphasis bg-success-subtle border border-success-subtle rounded-pill">
    <img class="rounded-circle me-1" width="24" height="24" src="https://github.com/mdo.png" alt="">Success
  </span>
  <span class="badge d-flex align-items-center p-1 pe-2 text-danger-emphasis bg-danger-subtle border border-danger-subtle rounded-pill">
    <img class="rounded-circle me-1" width="24" height="24" src="https://github.com/mdo.png" alt="">Danger
  </span>
  <span class="badge d-flex align-items-center p-1 pe-2 text-warning-emphasis bg-warning-subtle border border-warning-subtle rounded-pill">
    <img class="rounded-circle me-1" width="24" height="24" src="https://github.com/mdo.png" alt="">Warning
  </span>
  <span class="badge d-flex align-items-center p-1 pe-2 text-info-emphasis bg-info-subtle border border-info-subtle rounded-pill">
    <img class="rounded-circle me-1" width="24" height="24" src="https://github.com/mdo.png" alt="">Info
  </span>
  <span class="badge d-flex align-items-center p-1 pe-2 text-dark-emphasis bg-light-subtle border border-dark-subtle rounded-pill">
    <img class="rounded-circle me-1" width="24" height="24" src="https://github.com/mdo.png" alt="">Light
  </span>
  <span class="badge d-flex align-items-center p-1 pe-2 text-dark-emphasis bg-dark-subtle border border-dark-subtle rounded-pill">
    <img class="rounded-circle me-1" width="24" height="24" src="https://github.com/mdo.png" alt="">Dark
  </span>
</div>

<div class="b-example-divider"></div>

<div class="d-flex gap-2 justify-content-center py-5">
  <span class="badge d-flex p-2 align-items-center text-bg-primary rounded-pill">
    <span class="px-1">Primary</span>
    <a href="#"><svg class="bi ms-1" width="16" height="16"><use xlink:href="#x-circle-fill"/></svg></a>
  </span>
  <span class="badge d-flex p-2 align-items-center text-primary-emphasis bg-primary-subtle rounded-pill">
    <span class="px-1">Primary</span>
    <a href="#"><svg class="bi ms-1" width="16" height="16"><use xlink:href="#x-circle-fill"/></svg></a>
  </span>
  <span class="badge d-flex p-2 align-items-center text-primary-emphasis bg-primary-subtle border border-primary-subtle rounded-pill">
    <span class="px-1">Primary</span>
    <a href="#"><svg class="bi ms-1" width="16" height="16"><use xlink:href="#x-circle-fill"/></svg></a>
  </span>
</div>

<div class="b-example-divider"></div>

<div class="d-flex gap-2 justify-content-center py-5">
  <span class="badge d-flex align-items-center p-1 pe-2 text-primary-emphasis bg-primary-subtle border border-primary-subtle rounded-pill">
    <img class="rounded-circle me-1" width="24" height="24" src="https://github.com/mdo.png" alt="">
    Primary
    <span class="vr mx-2"></span>
    <a href="#"><svg class="bi" width="16" height="16"><use xlink:href="#x-circle-fill"/></svg></a>
  </span>
  <span class="badge d-flex align-items-center p-1 pe-2 text-secondary-emphasis bg-secondary-subtle border border-secondary-subtle rounded-pill">
    <img class="rounded-circle me-1" width="24" height="24" src="https://github.com/mdo.png" alt="">
    Secondary
    <span class="vr mx-2"></span>
    <a href="#"><svg class="bi" width="16" height="16"><use xlink:href="#x-circle-fill"/></svg></a>
  </span>
  <span class="badge d-flex align-items-center p-1 pe-2 text-success-emphasis bg-success-subtle border border-success-subtle rounded-pill">
    <img class="rounded-circle me-1" width="24" height="24" src="https://github.com/mdo.png" alt="">
    Success
    <span class="vr mx-2"></span>
    <a href="#"><svg class="bi" width="16" height="16"><use xlink:href="#x-circle-fill"/></svg></a>
  </span>
  <span class="badge d-flex align-items-center p-1 pe-2 text-danger-emphasis bg-danger-subtle border border-danger-subtle rounded-pill">
    <img class="rounded-circle me-1" width="24" height="24" src="https://github.com/mdo.png" alt="">
    Danger
    <span class="vr mx-2"></span>
    <a href="#"><svg class="bi" width="16" height="16"><use xlink:href="#x-circle-fill"/></svg></a>
  </span>
  <span class="badge d-flex align-items-center p-1 pe-2 text-warning-emphasis bg-warning-subtle border border-warning-subtle rounded-pill">
    <img class="rounded-circle me-1" width="24" height="24" src="https://github.com/mdo.png" alt="">
    Warning
    <span class="vr mx-2"></span>
    <a href="#"><svg class="bi" width="16" height="16"><use xlink:href="#x-circle-fill"/></svg></a>
  </span>
  <span class="badge d-flex align-items-center p-1 pe-2 text-info-emphasis bg-info-subtle border border-info-subtle rounded-pill">
    <img class="rounded-circle me-1" width="24" height="24" src="https://github.com/mdo.png" alt="">
    Info
    <span class="vr mx-2"></span>
    <a href="#"><svg class="bi" width="16" height="16"><use xlink:href="#x-circle-fill"/></svg></a>
  </span>
  <span class="badge d-flex align-items-center p-1 pe-2 text-light-emphasis bg-light-subtle border border-dark-subtle rounded-pill">
    <img class="rounded-circle me-1" width="24" height="24" src="https://github.com/mdo.png" alt="">
    Light
    <span class="vr mx-2"></span>
    <a href="#"><svg class="bi" width="16" height="16"><use xlink:href="#x-circle-fill"/></svg></a>
  </span>
  <span class="badge d-flex align-items-center p-1 pe-2 text-dark-emphasis bg-dark-subtle border border-dark-subtle rounded-pill">
    <img class="rounded-circle me-1" width="24" height="24" src="https://github.com/mdo.png" alt="">
    Dark
    <span class="vr mx-2"></span>
    <a href="#"><svg class="bi" width="16" height="16"><use xlink:href="#x-circle-fill"/></svg></a>
  </span>
</div>
<script src="../assets/dist/js/bootstrap.bundle.min.js"></script>

    </body>
</html>
