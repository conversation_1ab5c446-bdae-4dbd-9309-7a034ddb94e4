﻿<?xml version="1.0"?>
<doc>
<assembly>
<name>
Ami<PERSON><PERSON>ye
</name>
</assembly>
<members>
<member name="T:AmiBigeye.My.MyWebExtension">
 <summary>
 Mo<PERSON><PERSON> used to define the properties that are available in the My Namespace for Web projects.
 </summary>
 <remarks></remarks>
</member>
<member name="P:AmiBigeye.My.MyWebExtension.Computer">
 <summary>
 Returns information about the host computer.
 </summary>
</member>
<member name="P:AmiBigeye.My.MyWebExtension.User">
 <summary>
 Returns information for the current Web user.
 </summary>
</member>
<member name="P:AmiBigeye.My.MyWebExtension.Request">
 <summary>
 Returns Request object.
 </summary>
</member>
<member name="P:AmiBigeye.My.MyWebExtension.Response">
 <summary>
 Returns Response object.
 </summary>
</member>
<member name="P:AmiBigeye.My.MyWebExtension.Log">
 <summary>
 Returns the Asp log object.
 </summary>
</member>
<member name="T:AmiBigeye.My.Resources.Resources">
<summary>
  A strongly-typed resource class, for looking up localized strings, etc.
</summary>
</member>
<member name="P:AmiBigeye.My.Resources.Resources.ResourceManager">
<summary>
  Returns the cached ResourceManager instance used by this class.
</summary>
</member>
<member name="P:AmiBigeye.My.Resources.Resources.Culture">
<summary>
  Overrides the current thread's CurrentUICulture property for all
  resource lookups using this strongly typed resource class.
</summary>
</member>
<member name="F:AmiBigeye.SiteReview._ContentPlaceHolderReview">
<summary>
ContentPlaceHolderReview control.
</summary>
<remarks>
Auto-generated field.
To modify move field declaration from designer file to code-behind file.
</remarks>
</member>
<member name="F:AmiBigeye.Upload._DropDownListBrand">
<summary>
DropDownListBrand control.
</summary>
<remarks>
Auto-generated field.
To modify move field declaration from designer file to code-behind file.
</remarks>
</member>
<member name="F:AmiBigeye.Upload._SqlDataSourceBrand">
<summary>
SqlDataSourceBrand control.
</summary>
<remarks>
Auto-generated field.
To modify move field declaration from designer file to code-behind file.
</remarks>
</member>
<member name="F:AmiBigeye.Upload._DropDownListModel">
<summary>
DropDownListModel control.
</summary>
<remarks>
Auto-generated field.
To modify move field declaration from designer file to code-behind file.
</remarks>
</member>
<member name="F:AmiBigeye.Upload._SqlDataSourceModel">
<summary>
SqlDataSourceModel control.
</summary>
<remarks>
Auto-generated field.
To modify move field declaration from designer file to code-behind file.
</remarks>
</member>
<member name="F:AmiBigeye.Upload._fileUpload1">
<summary>
fileUpload1 control.
</summary>
<remarks>
Auto-generated field.
To modify move field declaration from designer file to code-behind file.
</remarks>
</member>
<member name="F:AmiBigeye.Upload._Image1">
<summary>
Image1 control.
</summary>
<remarks>
Auto-generated field.
To modify move field declaration from designer file to code-behind file.
</remarks>
</member>
<member name="F:AmiBigeye.Upload._Label1">
<summary>
Label1 control.
</summary>
<remarks>
Auto-generated field.
To modify move field declaration from designer file to code-behind file.
</remarks>
</member>
<member name="F:AmiBigeye.Upload._fileUpload2">
<summary>
fileUpload2 control.
</summary>
<remarks>
Auto-generated field.
To modify move field declaration from designer file to code-behind file.
</remarks>
</member>
<member name="F:AmiBigeye.Upload._Image2">
<summary>
Image2 control.
</summary>
<remarks>
Auto-generated field.
To modify move field declaration from designer file to code-behind file.
</remarks>
</member>
<member name="F:AmiBigeye.Upload._Label2">
<summary>
Label2 control.
</summary>
<remarks>
Auto-generated field.
To modify move field declaration from designer file to code-behind file.
</remarks>
</member>
<member name="F:AmiBigeye.Upload._fileUpload3">
<summary>
fileUpload3 control.
</summary>
<remarks>
Auto-generated field.
To modify move field declaration from designer file to code-behind file.
</remarks>
</member>
<member name="F:AmiBigeye.Upload._Image3">
<summary>
Image3 control.
</summary>
<remarks>
Auto-generated field.
To modify move field declaration from designer file to code-behind file.
</remarks>
</member>
<member name="F:AmiBigeye.Upload._Label3">
<summary>
Label3 control.
</summary>
<remarks>
Auto-generated field.
To modify move field declaration from designer file to code-behind file.
</remarks>
</member>
<member name="F:AmiBigeye.Upload._fileUpload4">
<summary>
fileUpload4 control.
</summary>
<remarks>
Auto-generated field.
To modify move field declaration from designer file to code-behind file.
</remarks>
</member>
<member name="F:AmiBigeye.Upload._Image4">
<summary>
Image4 control.
</summary>
<remarks>
Auto-generated field.
To modify move field declaration from designer file to code-behind file.
</remarks>
</member>
<member name="F:AmiBigeye.Upload._Label4">
<summary>
Label4 control.
</summary>
<remarks>
Auto-generated field.
To modify move field declaration from designer file to code-behind file.
</remarks>
</member>
<member name="F:AmiBigeye.Upload._fileUpload5">
<summary>
fileUpload5 control.
</summary>
<remarks>
Auto-generated field.
To modify move field declaration from designer file to code-behind file.
</remarks>
</member>
<member name="F:AmiBigeye.Upload._Image5">
<summary>
Image5 control.
</summary>
<remarks>
Auto-generated field.
To modify move field declaration from designer file to code-behind file.
</remarks>
</member>
<member name="F:AmiBigeye.Upload._Label5">
<summary>
Label5 control.
</summary>
<remarks>
Auto-generated field.
To modify move field declaration from designer file to code-behind file.
</remarks>
</member>
<member name="F:AmiBigeye.Upload._fileUpload6">
<summary>
fileUpload6 control.
</summary>
<remarks>
Auto-generated field.
To modify move field declaration from designer file to code-behind file.
</remarks>
</member>
<member name="F:AmiBigeye.Upload._Image6">
<summary>
Image6 control.
</summary>
<remarks>
Auto-generated field.
To modify move field declaration from designer file to code-behind file.
</remarks>
</member>
<member name="F:AmiBigeye.Upload._Label6">
<summary>
Label6 control.
</summary>
<remarks>
Auto-generated field.
To modify move field declaration from designer file to code-behind file.
</remarks>
</member>
<member name="F:AmiBigeye.Upload._fileUpload7">
<summary>
fileUpload7 control.
</summary>
<remarks>
Auto-generated field.
To modify move field declaration from designer file to code-behind file.
</remarks>
</member>
<member name="F:AmiBigeye.Upload._Image7">
<summary>
Image7 control.
</summary>
<remarks>
Auto-generated field.
To modify move field declaration from designer file to code-behind file.
</remarks>
</member>
<member name="F:AmiBigeye.Upload._Label7">
<summary>
Label7 control.
</summary>
<remarks>
Auto-generated field.
To modify move field declaration from designer file to code-behind file.
</remarks>
</member>
<member name="F:AmiBigeye.Upload._fileUpload8">
<summary>
fileUpload8 control.
</summary>
<remarks>
Auto-generated field.
To modify move field declaration from designer file to code-behind file.
</remarks>
</member>
<member name="F:AmiBigeye.Upload._Image8">
<summary>
Image8 control.
</summary>
<remarks>
Auto-generated field.
To modify move field declaration from designer file to code-behind file.
</remarks>
</member>
<member name="F:AmiBigeye.Upload._Label8">
<summary>
Label8 control.
</summary>
<remarks>
Auto-generated field.
To modify move field declaration from designer file to code-behind file.
</remarks>
</member>
<member name="F:AmiBigeye.Upload._fileUpload9">
<summary>
fileUpload9 control.
</summary>
<remarks>
Auto-generated field.
To modify move field declaration from designer file to code-behind file.
</remarks>
</member>
<member name="F:AmiBigeye.Upload._Image9">
<summary>
Image9 control.
</summary>
<remarks>
Auto-generated field.
To modify move field declaration from designer file to code-behind file.
</remarks>
</member>
<member name="F:AmiBigeye.Upload._Label9">
<summary>
Label9 control.
</summary>
<remarks>
Auto-generated field.
To modify move field declaration from designer file to code-behind file.
</remarks>
</member>
<member name="F:AmiBigeye.Upload._fileUpload10">
<summary>
fileUpload10 control.
</summary>
<remarks>
Auto-generated field.
To modify move field declaration from designer file to code-behind file.
</remarks>
</member>
<member name="F:AmiBigeye.Upload._Image10">
<summary>
Image10 control.
</summary>
<remarks>
Auto-generated field.
To modify move field declaration from designer file to code-behind file.
</remarks>
</member>
<member name="F:AmiBigeye.Upload._Label10">
<summary>
Label10 control.
</summary>
<remarks>
Auto-generated field.
To modify move field declaration from designer file to code-behind file.
</remarks>
</member>
<member name="F:AmiBigeye.Upload._fileUpload11">
<summary>
fileUpload11 control.
</summary>
<remarks>
Auto-generated field.
To modify move field declaration from designer file to code-behind file.
</remarks>
</member>
<member name="F:AmiBigeye.Upload._Image11">
<summary>
Image11 control.
</summary>
<remarks>
Auto-generated field.
To modify move field declaration from designer file to code-behind file.
</remarks>
</member>
<member name="F:AmiBigeye.Upload._Label11">
<summary>
Label11 control.
</summary>
<remarks>
Auto-generated field.
To modify move field declaration from designer file to code-behind file.
</remarks>
</member>
<member name="F:AmiBigeye.Upload._fileUpload12">
<summary>
fileUpload12 control.
</summary>
<remarks>
Auto-generated field.
To modify move field declaration from designer file to code-behind file.
</remarks>
</member>
<member name="F:AmiBigeye.Upload._Image12">
<summary>
Image12 control.
</summary>
<remarks>
Auto-generated field.
To modify move field declaration from designer file to code-behind file.
</remarks>
</member>
<member name="F:AmiBigeye.Upload._Label12">
<summary>
Label12 control.
</summary>
<remarks>
Auto-generated field.
To modify move field declaration from designer file to code-behind file.
</remarks>
</member>
<member name="F:AmiBigeye.Upload._Button1">
<summary>
Button1 control.
</summary>
<remarks>
Auto-generated field.
To modify move field declaration from designer file to code-behind file.
</remarks>
</member>
<member name="F:AmiBigeye.Upload._LabelUploadClicked">
<summary>
LabelUploadClicked control.
</summary>
<remarks>
Auto-generated field.
To modify move field declaration from designer file to code-behind file.
</remarks>
</member>
<member name="F:AmiBigeye.SiteMaster._head">
<summary>
head control.
</summary>
<remarks>
Auto-generated field.
To modify move field declaration from designer file to code-behind file.
</remarks>
</member>
<member name="F:AmiBigeye.SiteMaster._form1">
<summary>
form1 control.
</summary>
<remarks>
Auto-generated field.
To modify move field declaration from designer file to code-behind file.
</remarks>
</member>
<member name="F:AmiBigeye.SiteMaster._ContentPlaceHolder1">
<summary>
ContentPlaceHolder1 control.
</summary>
<remarks>
Auto-generated field.
To modify move field declaration from designer file to code-behind file.
</remarks>
</member>
<member name="F:AmiBigeye.Site_Mobile._Head1">
<summary>
Head1 control.
</summary>
<remarks>
Auto-generated field.
To modify move field declaration from designer file to code-behind file.
</remarks>
</member>
<member name="F:AmiBigeye.Site_Mobile._head">
<summary>
head control.
</summary>
<remarks>
Auto-generated field.
To modify move field declaration from designer file to code-behind file.
</remarks>
</member>
<member name="F:AmiBigeye.Site_Mobile._form1">
<summary>
form1 control.
</summary>
<remarks>
Auto-generated field.
To modify move field declaration from designer file to code-behind file.
</remarks>
</member>
<member name="F:AmiBigeye.Site_Mobile._FeaturedContent">
<summary>
FeaturedContent control.
</summary>
<remarks>
Auto-generated field.
To modify move field declaration from designer file to code-behind file.
</remarks>
</member>
<member name="F:AmiBigeye.Site_Mobile._ContentPlaceHolder1">
<summary>
ContentPlaceHolder1 control.
</summary>
<remarks>
Auto-generated field.
To modify move field declaration from designer file to code-behind file.
</remarks>
</member>
<member name="F:AmiBigeye.Site_Mobile._ViewSwitcher1">
<summary>
ViewSwitcher1 control.
</summary>
<remarks>
Auto-generated field.
To modify move field declaration from designer file to code-behind file.
</remarks>
</member>
</members>
</doc>
