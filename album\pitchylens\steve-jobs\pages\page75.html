<div class="book-content">

<p class="no-indent">
The system’s UNIX kernel was called Darwin, and it was based on Mach, the modern kernel technology developed by <PERSON><PERSON> at Carnegie Mellon
and the foundation of NeXTSTEP. Darwin was why Mac OS X had protected memory and pre-emptive multi-tasking, which allowed for multiple
applications to run at the same time without ever bringing the system down. It also provided very advanced networking, unlike the old Mac OS.
</p>
<p>
2D graphics were based on PostScript, just like NeXTSTEP, which allowed for
nice font anti-aliasing and on the-fly PDF rendering. 3D graphics however, unlike NeXTSTEP, were based on the most widespread standard, OpenGL,
not on Pixar’s RenderMan. And the media core was Apple’s QuickTime, an old Mac technology ported to the new system.
</p>
<p>
Object-oriented application development, which was the raison d’être of NeXTSTEP and its true competitive
</p>
</div>
<span class="page-number">75</span>