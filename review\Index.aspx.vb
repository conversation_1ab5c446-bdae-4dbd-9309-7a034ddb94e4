﻿Imports System.IO
Imports System.Net

Public Class Index
    Inherits System.Web.UI.Page
    Dim hostName As String = Dns.GetHostName()
    Public folderPath As String
    Public tom_filename() As String
    Public x As Integer = 0
    Public tom_x As Integer = 0
    Public tom_equal_filename() As String
    Public distinctArray() As String
    Public y As Integer = 0
    Public tom_y As Integer = 0
    Public tom_Distinct_filename() As String
    Public tom_ReturnReplace_Distinct_filename() As String
    Public z As Integer = 0
    Public Brand As String = "Witches Lens"



    Protected Sub Page_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load

        If hostName = "Tom_Home" Or hostName = "Tom" Then
            folderPath = "X:\!Review\"
        ElseIf hostName = "VColo" Then
            folderPath = "E:\!Infu\!Review"
        End If

        If Directory.Exists(folderPath) = True Then
            Dim directoryPath As String = folderPath
            Dim fileNames As String() = Directory.GetFiles(directoryPath)
            Dim orderedFiles = fileNames.OrderByDescending(Function(file) New FileInfo(file).LastWriteTime).ToList()
            For Each fileName As String In orderedFiles
                tom_x += 1
            Next

            ReDim tom_filename(tom_x)
            ReDim tom_equal_filename(tom_x)


            For Each fileName As String In orderedFiles

                tom_filename(x) = Path.GetFileName(fileName)
                tom_equal_filename(x) = tom_filename(x)

                '# ไม่ต้อง เพราะ ชื่อยี่ห้อ จะใส่อยู่ในชื่อไฟล์เลย
                'tom_equal_filename(x) = Replace(tom_equal_filename(x), "_(Witches_Lens)", "")

                'กำจัด 00.00_Gray_2 to 00.00_Gray
                'ถ้าไม่ทำแบบนี้ จะเจอว่า 11 โดนตัด 1 ออกแล้ว เหลือ 1 จะไม่โดยตัด
                'ต้องวิ่ง 2 ครั้ง เพราะ ตัวเลข มีอยู่ทั้ง สอง สี

                z = 111
                For z = 111 To 1000
                    tom_equal_filename(x) = Replace(tom_equal_filename(x), "_Pic" & z, "")
                Next

                z = 11
                For z = 11 To 110
                    tom_equal_filename(x) = Replace(tom_equal_filename(x), "_Pic" & z, "")
                Next

                z = 0
                For z = 0 To 10
                    tom_equal_filename(x) = Replace(tom_equal_filename(x), "_Pic" & z, "")
                Next

                'กำจัด )0 ไม่ได้ที่บันทัดนี้ เพราะ )0 ไม่เกิดจากบันทัดนี้
                'tom_equal_filename(x) = Replace(tom_equal_filename(x), ")0", "")


                z = 111
                For z = 111 To 1000
                    tom_equal_filename(x) = Replace(tom_equal_filename(x), "_Pic" & z, "")
                Next

                z = 11
                For z = 11 To 110
                    tom_equal_filename(x) = Replace(tom_equal_filename(x), "_Pic" & z, "")
                Next

                z = 0
                For z = 0 To 10
                    tom_equal_filename(x) = Replace(tom_equal_filename(x), "_Pic" & z, "")
                Next





                z = 11
                For z = 11 To 200
                    tom_equal_filename(x) = Replace(tom_equal_filename(x), "_Vdo" & z, "")
                Next

                z = 0
                For z = 0 To 10
                    tom_equal_filename(x) = Replace(tom_equal_filename(x), "_Vdo" & z, "")
                Next




                z = 11
                For z = 11 To 200
                    tom_equal_filename(x) = Replace(tom_equal_filename(x), "_Vdo" & z, "")
                Next

                z = 0
                For z = 0 To 10
                    tom_equal_filename(x) = Replace(tom_equal_filename(x), "_Vdo" & z, "")
                Next




                tom_equal_filename(x) = Replace(tom_equal_filename(x), ".jpg", "")

                tom_equal_filename(x) = Replace(tom_equal_filename(x), ".mp4", "")
                tom_equal_filename(x) = Replace(tom_equal_filename(x), ".mov", "")
                tom_equal_filename(x) = Replace(tom_equal_filename(x), ".MOV", "")
                tom_equal_filename(x) = Replace(tom_equal_filename(x), ".jpeg", "")
                tom_equal_filename(x) = Replace(tom_equal_filename(x), ".png", "")
                tom_equal_filename(x) = Replace(tom_equal_filename(x), ".avi", "")

                tom_equal_filename(x) = Replace(tom_equal_filename(x), "_Black", "")
                tom_equal_filename(x) = Replace(tom_equal_filename(x), "_Choco", "")
                tom_equal_filename(x) = Replace(tom_equal_filename(x), "_Gray", "")
                tom_equal_filename(x) = Replace(tom_equal_filename(x), "_Brown", "")
                tom_equal_filename(x) = Replace(tom_equal_filename(x), "_Blue", "")
                tom_equal_filename(x) = Replace(tom_equal_filename(x), "_Green", "")
                tom_equal_filename(x) = Replace(tom_equal_filename(x), "_Violet", "")
                tom_equal_filename(x) = Replace(tom_equal_filename(x), "_Pink", "")
                tom_equal_filename(x) = Replace(tom_equal_filename(x), "_Silver", "")
                tom_equal_filename(x) = Replace(tom_equal_filename(x), "_Gold", "")
                tom_equal_filename(x) = Replace(tom_equal_filename(x), "_Sky", "")
                tom_equal_filename(x) = Replace(tom_equal_filename(x), "_Red", "")

                'tom_equal_filename(x) = Replace(tom_equal_filename(x), "Thumbs.db", "")

                'กำจัด 0
                tom_equal_filename(x) = Replace(tom_equal_filename(x), "0", "")

                tom_equal_filename(x) = Replace(tom_equal_filename(x), "_", " ")



                x += 1
            Next


            Dim distinctArray = tom_equal_filename.Distinct().ToArray()
            For Each item In distinctArray
                tom_y += 1
            Next

            ReDim tom_Distinct_filename(tom_y)
            ReDim tom_ReturnReplace_Distinct_filename(tom_y)

            For Each item In distinctArray
                tom_Distinct_filename(y) = distinctArray(y)
                tom_ReturnReplace_Distinct_filename(y) = Replace(tom_Distinct_filename(y), " ", "_")
                y += 1
            Next


        Else
        End If

        x = 0
    End Sub

End Class


