<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Photo Album Viewer</title>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery/3.6.0/jquery.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/turn.js/3/turn.min.js"></script>
    <style>
        body {
            margin: 0;
            padding: 0;
            background: #333;
            overflow: hidden;
        }
        #album {
            width: 100%;
            height: 100vh;
        }
        .page {
            background-color: white;
        }
        .page img {
            max-width: 100%;
            height: 100%;
            object-fit: contain;
        }
        .turn-button {
            position: absolute;
            top: 50%;
            background: rgba(0,0,0,0.7);
            color: white;
            padding: 10px 15px;
            cursor: pointer;
            z-index: 1000;
        }
        #prev { left: 10px; }
        #next { right: 10px; }
    </style>
</head>
<body>
    <div id="album">
        <!-- Pages will be dynamically added here -->
    </div>
    <div id="prev" class="turn-button">&lt;</div>
    <div id="next" class="turn-button">&gt;</div>

    <script>
        // ลิสต์ของรูปภาพในอัลบั้ม (คุณจะต้องแทนที่ URL เหล่านี้ด้วย URL จริงของรูปภาพของคุณ)
        const images = [
            'https://via.placeholder.com/800x600?text=Page+1',
            'https://via.placeholder.com/800x600?text=Page+2',
            'https://via.placeholder.com/800x600?text=Page+3',
            'https://via.placeholder.com/800x600?text=Page+4',
            // เพิ่มรูปภาพเพิ่มเติมตามต้องการ
        ];

        // เพิ่มหน้าลงในอัลบั้ม
        images.forEach((img, index) => {
            $('#album').append(`<div class="page"><img src="${img}" alt="Page ${index + 1}"></div>`);
        });

        // กำหนดค่า Turn.js
        $(document).ready(function() {
            $('#album').turn({
                display: 'double',
                acceleration: true,
                gradients: !$.isTouch,
                elevation: 50,
                when: {
                    turned: function(e, page) {
                        console.log('Current page: ' + page);
                    }
                }
            });
        });

        // ปุ่มควบคุมการพลิกหน้า
        $('#prev').click(function() {
            $('#album').turn('previous');
        });

        $('#next').click(function() {
            $('#album').turn('next');
        });

        // ปรับขนาดเมื่อหน้าต่างเปลี่ยนขนาด
        $(window).resize(function() {
            var width = $('#album').parent().width();
            var height = $('#album').parent().height();
            $('#album').turn('size', width, height);
        });
    </script>
</body>
</html>
