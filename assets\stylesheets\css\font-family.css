@charset "UTF-8";
/* Generated by <PERSON>ont Squirrel (http://www.fontsquirrel.com) on February 2, 2015 */

@font-face {
  font-family: "SanamDek<PERSON>_chaya";
  src: url("../../fonts/SanamDeklen_chaya.eot");
  src: url('../../fonts/SanamDeklen_chaya.eot?#iefix') format('embedded-opentype'), url('../../fonts/SanamDeklen_chaya.woff2') format('woff2'), url('../../fonts/SanamDeklen_chaya.woff') format('woff'), url('../../fonts/SanamDeklen_chaya.ttf') format('truetype'), url('../../fonts/SanamDeklen_chaya.svg#montserratbold') format('svg');

  font-weight: normal;
  font-style: normal;
}

@font-face {
  font-family: 'montserratbold';
  src: url('../../fonts/montserrat-bold-webfont.eot');
  src: url('../../fonts/montserrat-bold-webfont.eot?#iefix') format('embedded-opentype'), url('../../fonts/montserrat-bold-webfont.woff2') format('woff2'), url('../../fonts/montserrat-bold-webfont.woff') format('woff'), url('../../fonts/montserrat-bold-webfont.ttf') format('truetype'), url('../../fonts/montserrat-bold-webfont.svg#montserratbold') format('svg');
  font-weight: normal;
  font-style: normal;
}
@font-face {
  font-family: 'moon_flower_boldregular';
  src: url('../../fonts/moon_flower_bold-webfont.eot');
  src: url('../../fonts/moon_flower_bold-webfont.eot?#iefix') format('embedded-opentype'), url('../../fonts/moon_flower_bold-webfont.woff2') format('woff2'), url('../../fonts/moon_flower_bold-webfont.woff') format('woff'), url('../../fonts/moon_flower_bold-webfont.ttf') format('truetype'), url('../../fonts/moon_flower_bold-webfont.svg#moon_flower_boldregular') format('svg');
  font-weight: normal;
  font-style: normal;
}
@font-face {
  font-family: 'latoregular';
  src: url('../../fonts/lato-regular-webfont.eot');
  src: url('../../fonts/lato-regular-webfont.eot?#iefix') format('embedded-opentype'), url('../../fonts/lato-regular-webfont.woff2') format('woff2'), url('../../fonts/lato-regular-webfont.woff') format('woff'), url('../../fonts/lato-regular-webfont.ttf') format('truetype'), url('../../fonts/lato-regular-webfont.svg#latoregular') format('svg');
  font-weight: normal;
  font-style: normal;
}
@font-face {
  font-family: "custom-font";
  src: url("../../fonts/custom-font.eot");
  src: url("../../fonts/custom-font.eot?#iefix") format("embedded-opentype"), url("../../fonts/custom-font.woff") format("woff"), url("../../fonts/custom-font.ttf") format("truetype"), url("../../fonts/custom-font.svg#untitled-font-4") format("svg");
  font-weight: normal;
  font-style: normal;
}
[data-icon]:before {
  font-family: "custom-font" !important;
  content: attr(data-icon);
  font-style: normal !important;
  font-weight: normal !important;
  font-variant: normal !important;
  text-transform: none !important;
  speak: none;
  line-height: 1;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}
[class^="icon-"]:before,
[class*=" icon-"]:before {
  font-family: "custom-font" !important;
  font-style: normal !important;
  font-weight: normal !important;
  font-variant: normal !important;
  text-transform: none !important;
  speak: none;
  line-height: 1;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}
.icon-ionicons:before {
  content: "a";
}
.icon-ionicons-1:before {
  content: "b";
}
.icon-ionicons-2:before {
  content: "c";
}
.icon-ionicons-3:before {
  content: "d";
}
.icon-hour-glass:before {
  content: "e";
}
.icon-phone-1:before {
  content: "f";
}
.icon-paypal:before {
  content: "g";
}
.icon-map-pin-5:before {
  content: "h";
}
.icon-html:before {
  content: "i";
}
.icon-hourglass:before {
  content: "j";
}
.icon-wrench:before {
  content: "k";
}
.icon-fontawesome-webfont:before {
  content: "l";
}
.icon-fontawesome-webfont-1:before {
  content: "m";
}
.icon-fontawesome-webfont-2:before {
  content: "n";
}
.icon-check-alt:before {
  content: "o";
}
.icon-fontawesome-webfont-3:before {
  content: "p";
}
.icon-cog:before {
  content: "q";
}
.icon-cog-1:before {
  content: "r";
}
.icon-device-mobile:before {
  content: "s";
}
.icon-placepin:before {
  content: "t";
}
.icon-paint-bucket:before {
  content: "u";
}
.icon-screen-desktop:before {
  content: "v";
}
.icon-behance:before {
  content: "w";
}
.icon-facebook:before {
  content: "x";
}
.icon-twitter:before {
  content: "y";
}
.icon-linkedin:before {
  content: "z";
}
.icon-gplus:before {
  content: "A";
}
.icon-mail:before {
  content: "B";
}
.icon-email-envelope:before {
  content: "C";
}
.icon-at-email:before {
  content: "D";
}
.icon-dribbble:before {
  content: "E";
}
.icon-dribbble-1:before {
  content: "F";
}
.icon-mark-github:before {
  content: "G";
}
.icon-octoface:before {
  content: "H";
}
.icon-squirrel:before {
  content: "I";
}
.icon-check-mark-2:before {
  content: "J";
}
.icon-check-mark:before {
  content: "K";
}
.icon-check-circle:before {
  content: "L";
}
.icon-pinterest:before {
  content: "M";
}
.icon-social-orkut:before {
  content: "N";
}
.icon-social-evernote:before {
  content: "O";
}
.icon-social-linkedin:before {
  content: "P";
}
.icon-social-tumblr:before {
  content: "Q";
}
.icon-social-treehouse:before {
  content: "R";
}
.icon-social-stumbleupon:before {
  content: "S";
}
.icon-social-rdio:before {
  content: "T";
}
.icon-social-twitter:before {
  content: "U";
}
.icon-social-vimeo:before {
  content: "V";
}
.icon-social-xbox:before {
  content: "W";
}
.icon-social-smashing-mag:before {
  content: "X";
}
.icon-social-skype:before {
  content: "Y";
}
.icon-chevron-right:before {
  content: "Z";
}
.icon-chevron-left:before {
  content: "0";
}
.icon-apple:before {
  content: "1";
}
.icon-cd:before {
  content: "2";
}
.icon-air:before {
  content: "3";
}
.icon-rocket:before {
  content: "4";
}
.icon-sound:before {
  content: "5";
}
.icon-eye:before {
  content: "6";
}
.icon-attachment:before {
  content: "7";
}
.icon-at:before {
  content: "8";
}
.icon-check:before {
  content: "9";
}
.icon-pen:before {
  content: "!";
}
