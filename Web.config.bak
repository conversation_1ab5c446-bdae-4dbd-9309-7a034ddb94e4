<?xml version="1.0" encoding="UTF-8"?>
<!--
  For more information on how to configure your ASP.NET application, please visit
  https://go.microsoft.com/fwlink/?LinkId=169433
  -->
<configuration>
  <!--
    For a description of web.config changes see http://go.microsoft.com/fwlink/?LinkId=235367.

    The following attributes can be set on the <httpRuntime> tag.
      <system.Web>
        <httpRuntime targetFramework="4.6.1" />
      </system.Web>
  -->
  <connectionStrings>
    <add connectionString="Data Source=***********;Initial Catalog=sugareye;Persist Security Info=True;User ID=sa;Password=********" name="sugareyeConnectionString" providerName="System.Data.SqlClient" />
  </connectionStrings>
  <system.web>
    <globalization culture="th-TH" enableClientBasedCulture="true" fileEncoding="utf-8" requestEncoding="utf-8" responseEncoding="utf-8" uiCulture="th-TH" />
    <compilation debug="true" defaultLanguage="vb" strict="false" explicit="true" targetFramework="4.6.1" />
    <!--
    Web.config: บางครั้งการตั้งค่าที่อยู่ภายในไฟล์ Web.config ของแอปพลิเคชัน ASP.NET สามารถบังคับใช้ขนาดไฟล์สูงสุด. ค้นหาการตั้งค่า maxRequestLength ภายใน Web.config และปรับขนาดตามที่คุณต้องการ.
    -->
    <httpRuntime targetFramework="4.6.1" maxRequestLength="**********" />
    <authentication mode="Windows" />
    <pages>
      <namespaces>
        <add namespace="System.Web.Optimization" />
      </namespaces>
      <controls>
        <add assembly="Microsoft.AspNet.Web.Optimization.WebForms" namespace="Microsoft.AspNet.Web.Optimization.WebForms" tagPrefix="webopt" />
      </controls>
    </pages>
    <identity impersonate="false" />
  </system.web>
  <runtime>
    <assemblyBinding xmlns="urn:schemas-microsoft-com:asm.v1">
      <dependentAssembly>
        <assemblyIdentity name="Antlr3.Runtime" publicKeyToken="eb42632606e9261f" />
        <bindingRedirect oldVersion="0.0.0.0-3.5.0.2" newVersion="3.5.0.2" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="Microsoft.Web.Infrastructure" publicKeyToken="31bf3856ad364e35" />
        <bindingRedirect oldVersion="0.0.0.0-*******" newVersion="*******" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="Newtonsoft.Json" publicKeyToken="30ad4fe6b2a6aeed" />
        <bindingRedirect oldVersion="0.0.0.0-12.0.0.0" newVersion="12.0.0.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="WebGrease" publicKeyToken="31bf3856ad364e35" />
        <bindingRedirect oldVersion="0.0.0.0-1.6.5135.21930" newVersion="1.6.5135.21930" />
      </dependentAssembly>
    </assemblyBinding>
  </runtime>
  <system.codedom>
    <compilers>
      <compiler language="c#;cs;csharp" extension=".cs" type="Microsoft.CodeDom.Providers.DotNetCompilerPlatform.CSharpCodeProvider, Microsoft.CodeDom.Providers.DotNetCompilerPlatform, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35" warningLevel="4" compilerOptions="/langversion:default /nowarn:1659;1699;1701" />
      <compiler language="vb;vbs;visualbasic;vbscript" extension=".vb" type="Microsoft.CodeDom.Providers.DotNetCompilerPlatform.VBCodeProvider, Microsoft.CodeDom.Providers.DotNetCompilerPlatform, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35" warningLevel="4" compilerOptions="/langversion:default /nowarn:41008 /define:_MYTYPE=\&quot;Web\&quot; /optionInfer+" />
    </compilers>
  </system.codedom>
  <system.webServer>
    <defaultDocument enabled="true">
      <files>
                <clear />
                <add value="index.aspx" />
                <add value="Default.aspx" />
      </files>
    </defaultDocument>
    <security>
      <requestFiltering>
        <!--
              การกำหนดค่าในระบบเพื่อป้องกันการส่งคำขอที่มีขนาดใหญ่เกินไป
              -->
        <requestLimits maxAllowedContentLength="**********" />
      </requestFiltering>
    </security>
  </system.webServer>
</configuration>