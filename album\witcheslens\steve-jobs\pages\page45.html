<div class="book-content">

<p class="no-indent">


The most advanced UNIX technology was being developed at Carnegie-Mellon, 

<img class="left-pic zoom-this" src="samples/steve-jobs/pics/17.jpg" width="172" height="188">

where <PERSON> hired some of his best programmers, such as
<PERSON><PERSON>. He was also told about object-oriented programming, a breakthrough from Xerox PARC which made software development very
fast and efficient. So <PERSON> knew his priorities for the NeXT operating system: it would be a UNIX object-oriented system — on top of which
would be added a graphical user interface, to make it user-friendly. These were the very ambitious foundations of NeXTSTEP, so ambitious that it
would take several years before they would give birth to a stable operating system.
</p>

<p>
Second, of course, was hardware. <PERSON> had been
</p>


</div>
<span class="page-number">45</span>