<div class="book-content">

<h1>Apple’s early days</h1>

<p class="capital">
The day he finished work on his first computer, <PERSON><PERSON> started working on an improved design, the future Apple II. The Apple II was based on the Apple I’s design, but in many ways it was a huge breakthrough.
</p>

<p>
First, it ran a lot faster with half as many chips. It also was the first computer that could produce color, with any color TV you would plug it into. It could
handle high-resolution graphics and sound, and had a BASIC interpreter built-in. In short, it was the first computer that anybody who knew the
BASIC programming language could use: it had what it took to launch the personal computing revolution.
</p>

<p>
The prototype for the Apple II was almost ready when <PERSON> and <PERSON><PERSON>
partook in the Personal Computer
</p>

</div>
<span class="page-number">25</span>