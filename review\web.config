<?xml version="1.0" encoding="UTF-8"?>
<configuration>
    <system.webServer>
        <directoryBrowse enabled="true" />
        <security>
            <requestFiltering>
                <fileExtensions>
                    <add fileExtension=".mov" allowed="true" />
                    <add fileExtension=".mp4" allowed="true" />
                    <add fileExtension=".jpeg" allowed="true" />
                    <add fileExtension=".jpg" allowed="true" />
                    <add fileExtension=".png" allowed="true" />
                    <add fileExtension=".avi" allowed="true" />
                </fileExtensions>
                <requestLimits maxAllowedContentLength="2400000000" maxUrl="32768" maxQueryString="16384" />
            </requestFiltering>
        </security>
    </system.webServer>
    <system.web>
        <identity impersonate="true" />
    </system.web>



</configuration>
