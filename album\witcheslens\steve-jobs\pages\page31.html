<div class="book-content">

<p class="no-indent">
<p>


<PERSON><PERSON> used it himself to write the 
<img class="left-pic zoom-this" src="samples/steve-jobs/pics/10.jpg" width="150" height="345">
first program to ever run on the machine, a game called Breakout. The eight expansion slots in Apple II made a difference, too. <PERSON><PERSON> decided to
implement them against <PERSON>’ will, and this proved a wise move, as they allowed for all kinds of new features and software to be added to the
machine. One of those features was Disk II, a floppy disk drive Apple started shipping in early 1978. It made the sharing and installing of new software
very easy — soon the supply of Apple II software was thriving.
</p>

<p>
But probably the most important push toward the
</p>
</div>
<span class="page-number">31</span>