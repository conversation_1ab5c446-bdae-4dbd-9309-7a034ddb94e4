﻿Imports System.IO
Imports System.Web.ModelBinding
Imports System.Linq
Imports System.Net

Public Class Download
    Inherits System.Web.UI.Page
    Dim hostName As String = Dns.GetHostName()
    Public folderPath As String
    Public tom_filename() As String
    Public x As Integer = 0
    Public tom_x As Integer = 0
    Public Model As String

    Protected Sub Page_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load

        If hostName = "Tom_Home" Or hostName = "Tom" Then
            folderPath = "X:\!Review\"
        ElseIf hostName = "VColo" Then
            folderPath = "E:\!Infu\!Review"
        End If

        Model = Request.QueryString("Model")
        Model = Replace(Model, " ", "_")

        ' tommy รอแก้ เอามาใช้แค่ชั่วคราว ก่อนที่เพลงจะทำรูปเท่านั้น เมื่อเพลงทำรูปเสร็จ ให้ remake
        Model = Replace(Model, "_(Witches_Lens)", "")

        If Directory.Exists(folderPath) = True Then

            Dim directoryPath As String = folderPath
            Dim searchTerm As String = Model
            Dim allFiles = Directory.GetFiles(directoryPath)

            ' กรองและเรียงลำดับไฟล์โดยใช้ LINQ
            Dim matchedFiles = allFiles _
                            .Where(Function(file) Path.GetFileName(file).IndexOf("" & searchTerm & "", StringComparison.OrdinalIgnoreCase) >= 0) _
                            .OrderByDescending(Function(file) New FileInfo(file).LastWriteTime) _
                            .ToList()

            For Each file In matchedFiles
                tom_x += 1
            Next

            ReDim tom_filename(tom_x)

            For Each file In matchedFiles
                tom_filename(x) = Path.GetFileName(file)
                x += 1
            Next


        Else
        End If

        'x = 0
    End Sub

End Class