<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Improved AnyFlip-style Viewer</title>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/turn.js/3/turn.min.js"></script>
    <style>
        body, html {
            margin: 0;
            padding: 0;
            width: 100%;
            height: 100%;
            overflow: hidden;
            background-color: #333;
            font-family: Arial, sans-serif;
        }
        #flipbook-container {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
        }
        #flipbook {
            margin: 0 auto;
        }
        #flipbook .page {
            background-color: white;
        }
        #flipbook .page img {
            width: 100%;
            height: 100%;
            object-fit: contain;
        }
        #controls {
            position: fixed;
            bottom: 20px;
            left: 50%;
            transform: translateX(-50%);
            display: flex;
            gap: 10px;
            z-index: 1000;
        }
        #controls button {
            background-color: rgba(255, 255, 255, 0.7);
            border: none;
            padding: 10px 15px;
            cursor: pointer;
            font-size: 16px;
        }
        #controls button:hover {
            background-color: rgba(255, 255, 255, 0.9);
        }
        #page-num {
            color: white;
            font-size: 16px;
            align-self: center;
        }
        .loading {
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            color: white;
            font-size: 24px;
        }
    </style>
</head>
<body>
    <div id="flipbook-container">
        <div id="flipbook">
            <!-- Pages will be dynamically added here -->
        </div>
    </div>
    <div id="controls">
        <button id="prev">◀ Previous</button>
        <span id="page-num"></span>
        <button id="next">Next ▶</button>
    </div>
    <div class="loading">Loading...</div>

    <script>
        const baseUrl = 'https://www.amibigeye.com/album/witcheslens/book1/pages/';
        const totalPages = 30;
        let flipbook;
        let loadedImages = 0;

        function loadApp() {
            const container = $('#flipbook-container');
            const flipbookElem = $('#flipbook');

            // Add pages to flipbook
            for (let i = 1; i <= totalPages; i++) {
                const img = new Image();
                img.onload = function() {
                    loadedImages++;
                    if (loadedImages === totalPages) {
                        initializeFlipbook();
                    }
                };
                img.onerror = function() {
                    console.error(`Failed to load image: ${baseUrl}${i}.jpg`);
                    loadedImages++;
                    if (loadedImages === totalPages) {
                        initializeFlipbook();
                    }
                };
                img.src = `${baseUrl}${i}.jpg`;
                flipbookElem.append(`<div class="page"><img src="${baseUrl}${i}.jpg" alt="Page ${i}"></div>`);
            }
        }

        function initializeFlipbook() {
            $('.loading').hide();
            const container = $('#flipbook-container');
            flipbook = $('#flipbook').turn({
                width: container.width(),
                height: container.height(),
                autoCenter: true,
                display: 'double',
                acceleration: true,
                gradients: true,
                elevation: 50,
                when: {
                    turned: function(e, page) {
                        updatePageNum();
                    }
                }
            });

            // Bind events
            $(window).resize(resizeViewport);
            $('#prev').click(() => flipbook.turn('previous'));
            $('#next').click(() => flipbook.turn('next'));

            resizeViewport();
            updatePageNum();
        }

        function resizeViewport() {
            const container = $('#flipbook-container');
            const flipbook = $('#flipbook');
            
            let width = $(window).width();
            let height = $(window).height();

            // Calculate the best fit for the flipbook
            const ratio = width / height;
            const idealRatio = 1.3328842664; // From AnyFlip example

            if (ratio < idealRatio) {
                width = height * idealRatio;
            } else {
                height = width / idealRatio;
            }

            flipbook.turn('size', width * 0.9, height * 0.9);
            
            // Center the flipbook
            container.css({
                width: `${width * 0.9}px`,
                height: `${height * 0.9}px`
            });
        }

        function updatePageNum() {
            const total = flipbook.turn('pages');
            const current = flipbook.turn('page');
            $('#page-num').text(`Page ${current} of ${total}`);
        }

        $(document).ready(loadApp);
    </script>
</body>
</html>
