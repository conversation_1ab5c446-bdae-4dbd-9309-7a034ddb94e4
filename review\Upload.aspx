﻿<%@ Page Title="File Upload for Influencer" Language="vb" AutoEventWireup="false" MasterPageFile="~/review/SiteReview.Master" CodeBehind="Upload.aspx.vb" Inherits="AmiBigeye.Upload" %>


<asp:Content ID="ContentReview" ContentPlaceHolderID="ContentPlaceHolderReview" runat="server">

    <div class="container" style="padding:20px">

        <h5>แบบฟอร์ม ส่งผลงาน <span class="badge bg-secondary">Upload</span></h5>

        <label for="exampleFormControlInput1" class="form-label" style="padding-top:20px; color:red; font-weight:bold">*กรุณาเลือก... ยี่ห้อที่ท่าน Review</label>
<%--<asp:Label ID="lblResult" runat="server"></asp:Label>--%>
        <asp:DropDownList ID="DropDownListBrand" runat="server" AutoPostBack="True" DataSourceID="SqlDataSourceBrand" DataTextField="brand" DataValueField="brand" CssClass="form-select" AppendDataBoundItems="True">
            <asp:ListItem Text="-- กรุณาเลือก ยี่ห้อ ให้ถูกต้อง --" Value="no" />
        </asp:DropDownList>
        <asp:SqlDataSource ID="SqlDataSourceBrand" runat="server" ConnectionString="<%$ ConnectionStrings:sugareyeConnectionString %>" SelectCommand="SELECT DISTINCT [brand] FROM [product] WHERE (([brand] = @brand1) OR ([brand] = @brand2) OR ([brand] = @brand3)) order by brand">
			<SelectParameters>
                <asp:Parameter DefaultValue="Witches Lens" Name="brand1" Type="String" />
                <asp:Parameter DefaultValue="Pitchy Lens" Name="brand2" Type="String" />
                <asp:Parameter DefaultValue="Pretty Doll" Name="brand3" Type="String" />
            </SelectParameters>
		</asp:SqlDataSource>

        <div class="mb-3" style="padding-top:30px">
            <label for="exampleFormControlInput1" class="form-label">ชื่อ Influencer (ผู้ทรงอิทธิพล) *</label>
            <input type="text" name="StrTextName" value="" autocomplete="on" class="form-control" id="exampleFormControlInput1" placeholder="กรอกชื่อ Facebook ของท่าน" maxlength="30">
        </div>

        <div class="mb-3">
            <label for="exampleFormControlInput1" class="form-label">เบอร์ติดต่อ *</label>
            <input type="tel" name="StrTextTel" value="" size="14" maxlength="10" onkeypress="return isNumberKey(event)" autocomplete="on" class="form-control" id="exampleFormControlInput2" placeholder="กรอบ เบอร์โทรของท่าน (เผื่อเราได้ร่วมงานโฆษณาในโอกาศหน้า)">
        </div>   
        
<%--        <div class="mb-3">
            <label for="exampleFormControlTextarea1" class="form-label">เลขที่บัญชี (ใช้สำหรับโอนเงินให้ท่าน หลังจากตรวจงานแล้ว งานผ่าน)</label>
            <textarea name="StrTextBookBank" class="form-control" id="exampleFormControlTextarea2" rows="2"></textarea>
        </div>--%>

        <div class="mb-3">
            <label for="exampleFormControlTextarea1" class="form-label">ขอคำแนะนำ และ ประสบการณ์ คอนแทคเลนส์ Witches Lens (เลนส์แม่มด) เราควรต้องปรับปรุงอะไรในสินค้า</label>
            <textarea name="StrTextComment" class="form-control" id="exampleFormControlTextarea1" rows="3"></textarea>
        </div>
               
        

        <br />
        <label for="exampleFormControlInput1" class="form-label" style="color:red; font-weight:bold">*กรุณาเลือก... ชื่อรุ่นคอนแทคเลนส์ ที่ Review ให้ถูกต้อง โดยดูชื่อรุ่นจาก ฝาขวด</label>
<%--            <select class="form-select" aria-label="Default select example">
            <option value="1" selected>เลือกรุ่นที่ท่าน Review</option>
            </select>--%>
        <asp:DropDownList ID="DropDownListModel" runat="server" DataSourceID="SqlDataSourceModel" DataTextField="model" DataValueField="model" AutoPostBack="false" CssClass="form-select" AppendDataBoundItems="false">
             <asp:ListItem Text="-- กรุณาเลือก ชื่อรุ่น ที่ใส่ ให้ถูกต้อง --" Value="no" />
        </asp:DropDownList>
        <asp:SqlDataSource ID="SqlDataSourceModel" runat="server" ConnectionString="<%$ ConnectionStrings:sugareyeConnectionString %>" SelectCommand="SELECT [model] FROM [product] WHERE ([brand] = @brand and Visible = 1) ORDER BY [model]">
            <SelectParameters>
                <asp:ControlParameter ControlID="DropDownListBrand" Name="brand" PropertyName="SelectedValue" Type="String" />
            </SelectParameters>
        </asp:SqlDataSource>
 

        <br />
        <label for="exampleFormControlInput1" class="form-label" style="color:red; font-weight:bold">*กรุณาเลือก... สี ให้ถูกต้อง</label>
        <select class="form-select" name="StrTextColor" aria-label="Default select example">
            <option value="no" selected>--- กรุณาเลือก สี ที่ review ให้ถูกต้อง ---</option>
            <option value="Gray">Gray</option>
            <option value="Brown">Brown</option>
        </select>

 <%--       <br />
        <label for="exampleFormControlInput1" class="form-label">จำนวนไฟล์ที่ต้องการ Upload</label>
        <asp:DropDownList ID="ddlSample" runat="server" AutoPostBack="True" OnSelectedIndexChanged="ddlSample_SelectedIndexChanged" CssClass="form-select">
            <asp:ListItem Text="1" Value="1" Selected></asp:ListItem>
            <asp:ListItem Text="2" Value="2"></asp:ListItem>
            <asp:ListItem Text="3" Value="3"></asp:ListItem>
            <asp:ListItem Text="4" Value="4"></asp:ListItem>
            <asp:ListItem Text="5" Value="5"></asp:ListItem>
            <asp:ListItem Text="6" Value="6"></asp:ListItem>
            <asp:ListItem Text="7" Value="7"></asp:ListItem>
            <asp:ListItem Text="8" Value="8"></asp:ListItem>
            <asp:ListItem Text="9" Value="9"></asp:ListItem>
            <asp:ListItem Text="10" Value="10"></asp:ListItem>
            <asp:ListItem Text="11" Value="11"></asp:ListItem>
            <asp:ListItem Text="12" Value="12"></asp:ListItem>
            <asp:ListItem Text="13" Value="13"></asp:ListItem>
            <asp:ListItem Text="14" Value="14"></asp:ListItem>
            <asp:ListItem Text="15" Value="15"></asp:ListItem>
            <asp:ListItem Text="16" Value="16"></asp:ListItem>
            <asp:ListItem Text="17" Value="17"></asp:ListItem>
            <asp:ListItem Text="18" Value="18"></asp:ListItem>
            <asp:ListItem Text="19" Value="19"></asp:ListItem>
            <asp:ListItem Text="20" Value="20"></asp:ListItem>
            <asp:ListItem Text="21" Value="21"></asp:ListItem>
            <asp:ListItem Text="22" Value="22"></asp:ListItem>
            <asp:ListItem Text="23" Value="23"></asp:ListItem>
            <asp:ListItem Text="24" Value="24"></asp:ListItem>
            <asp:ListItem Text="25" Value="25"></asp:ListItem>
            <asp:ListItem Text="26" Value="26"></asp:ListItem>
            <asp:ListItem Text="27" Value="27"></asp:ListItem>
            <asp:ListItem Text="28" Value="28"></asp:ListItem>
            <asp:ListItem Text="29" Value="29"></asp:ListItem>
            <asp:ListItem Text="30" Value="30"></asp:ListItem>
        </asp:DropDownList>--%>
<%--<asp:Label ID="lblResult" runat="server"></asp:Label>--%>


        <br />
        <div class="mb-3">
        <label for="formFileMultiple" class="form-label danger" style="color:#6666ff">ส่ง ภาพรีวิว เน้นให้เห็น คอนแทคเลนส์ ชัดๆ *ไม่ต้องใช้ app นะ* (1 รุ่นต่อ 5 ภาพ ขึ้นไป)</label>

        <asp:FileUpload ID="fileUpload1" runat="server" AllowMultiple="False" ClientIDMode="AutoID" CssClass="form-control"/>
        <asp:Image ID="Image1" runat="server" Height = "150" Width = "150" Visible="False" />
        <asp:Label ID="Label1" runat="server" ForeColor="Red"></asp:Label>

        <asp:FileUpload ID="fileUpload2" runat="server" AllowMultiple="False" ClientIDMode="AutoID" CssClass="form-control"/>
        <asp:Image ID="Image2" runat="server" Height = "150" Width = "150" Visible="False" />
        <asp:Label ID="Label2" runat="server" ForeColor="Red"></asp:Label>

        <asp:FileUpload ID="fileUpload3" runat="server" AllowMultiple="False" ClientIDMode="AutoID" CssClass="form-control"/>
        <asp:Image ID="Image3" runat="server" Height = "150" Width = "150" Visible="False" />
        <asp:Label ID="Label3" runat="server" ForeColor="Red"></asp:Label>

        <asp:FileUpload ID="fileUpload4" runat="server" AllowMultiple="False" ClientIDMode="AutoID" CssClass="form-control"/>
        <asp:Image ID="Image4" runat="server" Height = "150" Width = "150" Visible="False" />
        <asp:Label ID="Label4" runat="server" ForeColor="Red"></asp:Label>

        <asp:FileUpload ID="fileUpload5" runat="server" AllowMultiple="False" ClientIDMode="AutoID" CssClass="form-control"/>
        <asp:Image ID="Image5" runat="server" Height = "150" Width = "150" Visible="False" />
        <asp:Label ID="Label5" runat="server" ForeColor="Red"></asp:Label>
            
        <asp:FileUpload ID="fileUpload6" runat="server" AllowMultiple="False" ClientIDMode="AutoID" CssClass="form-control"/>
        <asp:Image ID="Image6" runat="server" Height = "150" Width = "150" Visible="False" />
        <asp:Label ID="Label6" runat="server" ForeColor="Red"></asp:Label>

        <asp:FileUpload ID="fileUpload7" runat="server" AllowMultiple="False" ClientIDMode="AutoID" CssClass="form-control"/>
        <asp:Image ID="Image7" runat="server" Height = "150" Width = "150" Visible="False" />
        <asp:Label ID="Label7" runat="server" ForeColor="Red"></asp:Label>

        <asp:FileUpload ID="fileUpload8" runat="server" AllowMultiple="False" ClientIDMode="AutoID" CssClass="form-control"/>
        <asp:Image ID="Image8" runat="server" Height = "150" Width = "150" Visible="False" />
        <asp:Label ID="Label8" runat="server" ForeColor="Red"></asp:Label>

        <asp:FileUpload ID="fileUpload9" runat="server" AllowMultiple="False" ClientIDMode="AutoID" CssClass="form-control"/>
        <asp:Image ID="Image9" runat="server" Height = "150" Width = "150" Visible="False" />
        <asp:Label ID="Label9" runat="server" ForeColor="Red"></asp:Label>

        <asp:FileUpload ID="fileUpload10" runat="server" AllowMultiple="False" ClientIDMode="AutoID" CssClass="form-control"/>
        <asp:Image ID="Image10" runat="server" Height = "150" Width = "150" Visible="False" />
        <asp:Label ID="Label10" runat="server" ForeColor="Red"></asp:Label>


        <br /><br />
        <div class="mb-3">
		<!--  การขยายขนาดไฟล์ แก้ได้ที่ไฟล์ web.config  -->
        <label for="formFileMultiple" class="form-label" style="color:#ff6666">ส่ง งาน Video เน้นภาพชัด เห็นคอนแทคเลนส์ชัดๆ  1 รุ่น ต่อ 1 วิดีโอ หรือ จะส่งมา 2 วิดีโอ ก็ได้นะ  *ไม่ต้องใช้ app นะ* (ขนาดไฟล์ไม่เกิน  1GB. และ ขณะเลือกไฟล์ Vdo ในอัลบัม-วิดีโอ ของตัวเอง หลังจากกด เสร็จสิ้นแล้ว กรุณารอให้มันหมุนโหลดให้เสร็จก่อน อย่าพึ่งกดย้อนกลับนะครับ)</label>

        <asp:FileUpload ID="fileUpload11" runat="server" AllowMultiple="False" CssClass="form-control"/>
        <asp:Image ID="Image11" runat="server" Height = "150" Width = "150" Visible="False" />
        <asp:Label ID="Label11" runat="server" ForeColor="Red"></asp:Label>

        <asp:FileUpload ID="fileUpload12" runat="server" AllowMultiple="False" CssClass="form-control"/>
        <asp:Image ID="Image12" runat="server" Height = "150" Width = "150" Visible="False" />
        <asp:Label ID="Label12" runat="server" ForeColor="Red"></asp:Label>



        <br /><br />
        &nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp<asp:Button ID="Button1" runat="server" Text="Upload" OnClick="UploadFile" CssClass="btn btn-primary"/>&nbsp&nbsp&nbsp&nbsp&nbsp<asp:Label ID="LabelUploadClicked" runat="server" ForeColor="White"></asp:Label>            
            
    </div>

            <script language="javascript">
            var s1 = '<%=errormsg%>';
                if (s1 != "") {
                    alert(s1);
                }
            </script>

</div>
    </div>
</asp:Content>
