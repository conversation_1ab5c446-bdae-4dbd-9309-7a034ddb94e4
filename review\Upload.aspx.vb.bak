﻿Imports System.IO
Imports System
Imports System.Web.ModelBinding
Imports System.Runtime.InteropServices.ComTypes
Imports System.Net
'Imports System.Reflection.Emit


'รอทำ
'Private Sub SaveToFile(data As String)   ตัด .jpg .jpeg ออก

Public Class Upload
    Inherits System.Web.UI.Page
    Dim hostName As String = Dns.GetHostName()
    Public folderPath As String = ""
    Public URLPath As String = ""
    Public errormsg As String = ""
    'Public TextPath As String = ""
    Public NewFileNameExtension As String

    Public Tom_Pic_Finish As Integer = 0
    Public Tom_Vdo_Finish As Integer = 0

    Protected Sub Page_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load

        If hostName = "Tom_Home" Or hostName = "Tom" Then
            folderPath = "Y:\!Infu_Receive\"
            'IIS Express on VB ไม่สามารถสร้าง virtual drive ได้ และ MapPath มันหมายถึง Local Disk ไม่ใช่ URL
            'URLPath = Server.MapPath("~/!Infu_Receive/")
        ElseIf hostName = "VColo" Then
            folderPath = "E:\!Infu\!Infu_Receive\"
            URLPath = "/!Infu_Receive/"
        End If

        If Not IsPostBack Then
            ' กำหนดให้ตัวเลือกแรกเป็นค่าเริ่มต้นที่ถูกเลือก
            DropDownListBrand.Items.FindByText("-- กรุณาเลือก ยี่ห้อ ให้ถูกต้อง --").Selected = True
            DropDownListModel.Items.FindByText("-- กรุณาเลือก ชื่อรุ่น ที่ใส่ ให้ถูกต้อง --").Selected = True
        Else

        End If

        'If IsPostBack = True Then
        'ยังมีปัญหา มันชนกับเวลา submit
        'errormsg = "กรุณากรอกข้อมูลให้ครบถ้วน"
        'Else
        'DropDownListBrand.Text = "no"
        'End If

    End Sub

    'Protected Sub ddlSample_SelectedIndexChanged(sender As Object, e As EventArgs) Handles ddlSample.SelectedIndexChanged
    'lblResult.Text = "You selected: " & ddlSample.SelectedItem.Text
    'End Sub

    Private Sub SaveToFile(data As String)
        'TextPath = Server.MapPath("~/UploadedFiles/Data.txt") ' ตำแหน่งเซฟไฟล์, สามารถปรับแก้ไขได้

        'เอา นามสกุลออกจากไฟล์ text
        NewFileNameExtension = Replace(NewFileNameExtension, ".jpg", "")
        NewFileNameExtension = Replace(NewFileNameExtension, ".mp4", "")
        NewFileNameExtension = Replace(NewFileNameExtension, ".mov", "")
        NewFileNameExtension = Replace(NewFileNameExtension, ".jpeg", "")
        NewFileNameExtension = Replace(NewFileNameExtension, ".png", "")
        NewFileNameExtension = Replace(NewFileNameExtension, ".avi", "")
        System.IO.File.AppendAllText(folderPath & NewFileNameExtension & ".txt", data & Environment.NewLine)
    End Sub

    Protected Sub UploadFile(sender As Object, e As EventArgs) Handles Button1.Click

        Dim StrTextName As String = Request.Form("StrTextName")
        Dim StrTextTel As String = Request.Form("StrTextTel")
        Dim StrTextComment As String = Request.Form("StrTextComment")
        Dim StrTextBrand As String = Request.Form("StrTextBrand")
        Dim StrTextModel As String = DropDownListModel.Text
        Dim StrTextColor As String = Request.Form("StrTextColor")
        Dim StrTextBookBank As String = Request.Form("StrTextBookBank")

        Tom_Pic_Finish = 0
        Tom_Vdo_Finish = 0


        If Len(StrTextName) < 2 Or Len(StrTextTel) < 10 Then
            errormsg = "กรุณากรอก ชื่อ และ เบอร์โทร ให้ครบก่อนจ้า"

        ElseIf DropDownListBrand.Text = "no" Then
            errormsg = "กรุณา ยี่ห้อ ให้ถูกต้อง"

        ElseIf DropDownListModel.Text = "no" Then
            errormsg = "กรุณา ชื่อรุ่น ให้ถูกต้อง"

        ElseIf StrTextColor = "no" Then
            errormsg = "กรุณา สี ให้ถูกต้อง"

        Else

            Dim Extension As String


            Dim currentTime As DateTime = DateTime.Now
            'ไม่ใช้ fffffff เพราะทั้ง 10 ภาพ มันทำงานแค่ 0.000001 sec
            'Dim formattedTime As String = currentTime.ToString("yyyy-MM-dd_HH-mm-ss_fffffff")
            Dim formattedTime As String = currentTime.ToString("yyyy-MM-dd_HH-mm-ss")

            'Dim folderPath As String = Server.MapPath("~/Files/")

            ''Check whether Directory (Folder) exists.
            'If Not Directory.Exists(folderPath) Then
            '    'If Directory (Folder) does not exists. Create it.
            '    Directory.CreateDirectory(folderPath)
            'End If

            ''Save the File to the Directory (Folder).
            'FileUpload1.SaveAs(folderPath & Path.GetFileName(FileUpload1.FileName))

            ''Display the success message.
            'lblMessage.Text = Path.GetFileName(FileUpload1.FileName) + " has been uploaded."


            'Dim folderPath As String = Server.MapPath("~/Files/")

            ''Check whether Directory (Folder) exists.
            'If Not Directory.Exists(folderPath) Then
            '    'If Directory (Folder) does not exists Create it.
            '    Directory.CreateDirectory(folderPath)
            'End If

            ''Save the File to the Directory (Folder).
            'fileUpload1.SaveAs(folderPath & Path.GetFileName(fileUpload1.FileName))

            ''Display the Picture in Image control.
            'Image1.ImageUrl = "~/Files/" & Path.GetFileName(fileUpload1.FileName)


            'openFileDialog.Filter = "Image Files|*.jpg;*.jpeg;*.png;*.bmp;*.gif|Video Files|*.mp4;*.avi;*.mkv;*.flv;*.mov|All Files|*.*"



            If fileUpload1.HasFile Then
                Dim fileExt As String = System.IO.Path.GetExtension(fileUpload1.FileName).ToLower()
                If fileExt = ".jpeg" Or fileExt = ".jpg" Or fileExt = ".png" Or fileExt = ".gif" Or fileExt = ".webp" Or fileExt = ".raw" Or fileExt = ".heif" Then
                    'Dim savePath As String = Server.MapPath("~/UploadedFiles/")
                    Dim savePath As String = folderPath
                    Dim fileName As String = System.IO.Path.GetFileName(fileUpload1.FileName)
                    ' #Original
                    'savePath += fileName

                    If Right(fileName, 5) = ".jpeg" Then
                        Extension = Right(fileName, 5)
                    Else
                        Extension = Right(fileName, 4)
                    End If

                    NewFileNameExtension = Replace(StrTextModel, " ", "_") & "_(" & Replace(StrTextBrand, " ", "_") & ")_" & StrTextColor & "_Pic1_" & Replace(StrTextName, " ", "_") & "_" & formattedTime & "_1" & Extension
                    savePath += NewFileNameExtension

                    'Response.Write(savePath & "<br>" & NewFileNameExtension)

                    Try
                        fileUpload1.SaveAs(savePath)
                        Label1.Text = "File uploaded successfully!"
                        Tom_Pic_Finish = Tom_Pic_Finish + 1

                        Image1.Visible = 1
                        Image1.ImageUrl = URLPath & NewFileNameExtension
                    Catch ex As Exception
                        Label1.Text = "Error uploading file: " & ex.Message
                    End Try
                Else
                    Label1.Text = "Only Picture files are allowed."
                End If
            Else
                Label1.Text = "Please select a file first."
            End If


            'des to text
            SaveToFile("ชื่อ Facebook Infu: " & StrTextName & Environment.NewLine & "เบอร์โทร: " & StrTextTel & Environment.NewLine & "คำเสนอแนะ (ให้มาบอกพี่ทอม): " & StrTextComment & Environment.NewLine & Environment.NewLine & "เลขที่บัญชีธนาคาร (ถ้างานสมบูรณ์แล้วเรียกพี่นุ่นให้โอนเงิน และกลับมาบันทึกว่าโอนเงินแล้วในไฟล์นี้): " & Environment.NewLine & StrTextBookBank & Environment.NewLine & Environment.NewLine & "โอนเงินให้ Infu ยัง: " & Environment.NewLine & Environment.NewLine & "เริ่มลงไว้ที่ Pic สี Gray เท่าไหร่: " & Environment.NewLine & "เริ่มลงไว้ที่ Pic สี Brown เท่าไหร่: " & Environment.NewLine & Environment.NewLine & "เริ่มลงไว้ที่ Vdo สี Gray ที่เท่าไหร่: " & Environment.NewLine & "เริ่มลงไว้ที่ Vdo สี Brown ที่เท่าไหร่: ")


            If fileUpload2.HasFile Then
                Dim fileExt As String = System.IO.Path.GetExtension(fileUpload2.FileName).ToLower()
                If fileExt = ".jpeg" Or fileExt = ".jpg" Or fileExt = ".png" Or fileExt = ".gif" Or fileExt = ".webp" Or fileExt = ".raw" Or fileExt = ".heif" Then
                    'Dim savePath As String = Server.MapPath("~/UploadedFiles/")
                    Dim savePath As String = folderPath
                    Dim fileName As String = System.IO.Path.GetFileName(fileUpload2.FileName)
                    ' #Original
                    'savePath += fileName

                    If Right(fileName, 5) = ".jpeg" Then
                        Extension = Right(fileName, 5)
                    ElseIf Right(fileName, 5) = ".webp" Then
                        Extension = Right(fileName, 5)
                    ElseIf Right(fileName, 5) = ".heif" Then
                        Extension = Right(fileName, 5)
                    Else
                        Extension = Right(fileName, 4)
                    End If

                    NewFileNameExtension = Replace(StrTextModel, " ", "_") & "_(" & Replace(StrTextBrand, " ", "_") & ")_" & StrTextColor & "_Pic2_" & Replace(StrTextName, " ", "_") & "_" & formattedTime & "_2" & Extension
                    savePath += NewFileNameExtension

                    'Response.Write(savePath & "<br>" & NewFileNameExtension)

                    Try
                        fileUpload2.SaveAs(savePath)
                        Label2.Text = "File uploaded successfully!"
                        Tom_Pic_Finish = Tom_Pic_Finish + 1

                        Image2.Visible = 1
                        Image2.ImageUrl = URLPath & NewFileNameExtension
                    Catch ex As Exception
                        Label2.Text = "Error uploading file: " & ex.Message
                    End Try
                Else
                    Label2.Text = "Only Picture files are allowed."
                End If
            Else
                Label2.Text = "Please select a file first."
            End If


            If fileUpload3.HasFile Then
                Dim fileExt As String = System.IO.Path.GetExtension(fileUpload3.FileName).ToLower()
                If fileExt = ".jpeg" Or fileExt = ".jpg" Or fileExt = ".png" Or fileExt = ".gif" Or fileExt = ".webp" Or fileExt = ".raw" Or fileExt = ".heif" Then
                    'Dim savePath As String = Server.MapPath("~/UploadedFiles/")
                    Dim savePath As String = folderPath
                    Dim fileName As String = System.IO.Path.GetFileName(fileUpload3.FileName)
                    ' #Original
                    'savePath += fileName

                    If Right(fileName, 5) = ".jpeg" Then
                        Extension = Right(fileName, 5)
                    ElseIf Right(fileName, 5) = ".webp" Then
                        Extension = Right(fileName, 5)
                    ElseIf Right(fileName, 5) = ".heif" Then
                        Extension = Right(fileName, 5)
                    Else
                        Extension = Right(fileName, 4)
                    End If

                    NewFileNameExtension = Replace(StrTextModel, " ", "_") & "_(" & Replace(StrTextBrand, " ", "_") & ")_" & StrTextColor & "_Pic3_" & Replace(StrTextName, " ", "_") & "_" & formattedTime & "_3" & Extension
                    savePath += NewFileNameExtension

                    'Response.Write(savePath & "<br>" & NewFileNameExtension)

                    Try
                        fileUpload3.SaveAs(savePath)
                        Label3.Text = "File uploaded successfully!"
                        Tom_Pic_Finish = Tom_Pic_Finish + 1

                        Image3.Visible = 1
                        Image3.ImageUrl = URLPath & NewFileNameExtension
                    Catch ex As Exception
                        Label3.Text = "Error uploading file: " & ex.Message
                    End Try
                Else
                    Label3.Text = "Only Picture files are allowed."
                End If
            Else
                Label3.Text = "Please select a file first."
            End If


            If fileUpload4.HasFile Then
                Dim fileExt As String = System.IO.Path.GetExtension(fileUpload4.FileName).ToLower()
                If fileExt = ".jpeg" Or fileExt = ".jpg" Or fileExt = ".png" Or fileExt = ".gif" Or fileExt = ".webp" Or fileExt = ".raw" Or fileExt = ".heif" Then
                    'Dim savePath As String = Server.MapPath("~/UploadedFiles/")
                    Dim savePath As String = folderPath
                    Dim fileName As String = System.IO.Path.GetFileName(fileUpload4.FileName)
                    ' #Original
                    'savePath += fileName

                    If Right(fileName, 5) = ".jpeg" Then
                        Extension = Right(fileName, 5)
                    ElseIf Right(fileName, 5) = ".webp" Then
                        Extension = Right(fileName, 5)
                    ElseIf Right(fileName, 5) = ".heif" Then
                        Extension = Right(fileName, 5)
                    Else
                        Extension = Right(fileName, 4)
                    End If

                    NewFileNameExtension = Replace(StrTextModel, " ", "_") & "_(" & Replace(StrTextBrand, " ", "_") & ")_" & StrTextColor & "_Pic4_" & Replace(StrTextName, " ", "_") & "_" & formattedTime & "_4" & Extension
                    savePath += NewFileNameExtension

                    'Response.Write(savePath & "<br>" & NewFileNameExtension)

                    Try
                        fileUpload4.SaveAs(savePath)
                        Label4.Text = "File uploaded successfully!"
                        Tom_Pic_Finish = Tom_Pic_Finish + 1

                        Image4.Visible = 1
                        Image4.ImageUrl = URLPath & NewFileNameExtension
                    Catch ex As Exception
                        Label4.Text = "Error uploading file: " & ex.Message
                    End Try
                Else
                    Label4.Text = "Only Picture files are allowed."
                End If
            Else
                Label4.Text = "Please select a file first."
            End If


            If fileUpload5.HasFile Then
                Dim fileExt As String = System.IO.Path.GetExtension(fileUpload5.FileName).ToLower()
                If fileExt = ".jpeg" Or fileExt = ".jpg" Or fileExt = ".png" Or fileExt = ".gif" Or fileExt = ".webp" Or fileExt = ".raw" Or fileExt = ".heif" Then
                    'Dim savePath As String = Server.MapPath("~/UploadedFiles/")
                    Dim savePath As String = folderPath
                    Dim fileName As String = System.IO.Path.GetFileName(fileUpload5.FileName)
                    ' #Original
                    'savePath += fileName

                    If Right(fileName, 5) = ".jpeg" Then
                        Extension = Right(fileName, 5)
                    ElseIf Right(fileName, 5) = ".webp" Then
                        Extension = Right(fileName, 5)
                    ElseIf Right(fileName, 5) = ".heif" Then
                        Extension = Right(fileName, 5)
                    Else
                        Extension = Right(fileName, 4)
                    End If

                    NewFileNameExtension = Replace(StrTextModel, " ", "_") & "_(" & Replace(StrTextBrand, " ", "_") & ")_" & StrTextColor & "_Pic5_" & Replace(StrTextName, " ", "_") & "_" & formattedTime & "_5" & Extension
                    savePath += NewFileNameExtension

                    'Response.Write(savePath & "<br>" & NewFileNameExtension)

                    Try
                        fileUpload5.SaveAs(savePath)
                        Label5.Text = "File uploaded successfully!"
                        Tom_Pic_Finish = Tom_Pic_Finish + 1

                        Image5.Visible = 1
                        Image5.ImageUrl = URLPath & NewFileNameExtension
                    Catch ex As Exception
                        Label5.Text = "Error uploading file: " & ex.Message
                    End Try
                Else
                    Label5.Text = "Only Picture files are allowed."
                End If
            Else
                Label5.Text = "Please select a file first."
            End If



            If fileUpload6.HasFile Then
                Dim fileExt As String = System.IO.Path.GetExtension(fileUpload6.FileName).ToLower()
                If fileExt = ".jpeg" Or fileExt = ".jpg" Or fileExt = ".png" Or fileExt = ".gif" Or fileExt = ".webp" Or fileExt = ".raw" Or fileExt = ".heif" Then
                    'Dim savePath As String = Server.MapPath("~/UploadedFiles/")
                    Dim savePath As String = folderPath
                    Dim fileName As String = System.IO.Path.GetFileName(fileUpload6.FileName)
                    ' #Original
                    'savePath += fileName

                    If Right(fileName, 5) = ".jpeg" Then
                        Extension = Right(fileName, 5)
                    ElseIf Right(fileName, 5) = ".webp" Then
                        Extension = Right(fileName, 5)
                    ElseIf Right(fileName, 5) = ".heif" Then
                        Extension = Right(fileName, 5)
                    Else
                        Extension = Right(fileName, 4)
                    End If

                    NewFileNameExtension = Replace(StrTextModel, " ", "_") & "_(" & Replace(StrTextBrand, " ", "_") & ")_" & StrTextColor & "_Pic6_" & Replace(StrTextName, " ", "_") & "_" & formattedTime & "_6" & Extension
                    savePath += NewFileNameExtension

                    'Response.Write(savePath & "<br>" & NewFileNameExtension)

                    Try
                        fileUpload6.SaveAs(savePath)
                        Label6.Text = "File uploaded successfully!"
                        Tom_Pic_Finish = Tom_Pic_Finish + 1

                        Image6.Visible = 1
                        Image6.ImageUrl = URLPath & NewFileNameExtension
                    Catch ex As Exception
                        Label6.Text = "Error uploading file: " & ex.Message
                    End Try
                Else
                    Label6.Text = "Only Picture files are allowed."
                End If
            Else
                Label6.Text = "Please select a file first."
            End If


            If fileUpload7.HasFile Then
                Dim fileExt As String = System.IO.Path.GetExtension(fileUpload7.FileName).ToLower()
                If fileExt = ".jpeg" Or fileExt = ".jpg" Or fileExt = ".png" Or fileExt = ".gif" Or fileExt = ".webp" Or fileExt = ".raw" Or fileExt = ".heif" Then
                    'Dim savePath As String = Server.MapPath("~/UploadedFiles/")
                    Dim savePath As String = folderPath
                    Dim fileName As String = System.IO.Path.GetFileName(fileUpload7.FileName)
                    ' #Original
                    'savePath += fileName

                    If Right(fileName, 5) = ".jpeg" Then
                        Extension = Right(fileName, 5)
                    ElseIf Right(fileName, 5) = ".webp" Then
                        Extension = Right(fileName, 5)
                    ElseIf Right(fileName, 5) = ".heif" Then
                        Extension = Right(fileName, 5)
                    Else
                        Extension = Right(fileName, 4)
                    End If

                    NewFileNameExtension = Replace(StrTextModel, " ", "_") & "_(" & Replace(StrTextBrand, " ", "_") & ")_" & StrTextColor & "_Pic7_" & Replace(StrTextName, " ", "_") & "_" & formattedTime & "_7" & Extension
                    savePath += NewFileNameExtension

                    'Response.Write(savePath & "<br>" & NewFileNameExtension)

                    Try
                        fileUpload7.SaveAs(savePath)
                        Label7.Text = "File uploaded successfully!"
                        Tom_Pic_Finish = Tom_Pic_Finish + 1

                        Image7.Visible = 1
                        Image7.ImageUrl = URLPath & NewFileNameExtension
                    Catch ex As Exception
                        Label7.Text = "Error uploading file: " & ex.Message
                    End Try
                Else
                    Label7.Text = "Only Picture files are allowed."
                End If
            Else
                Label7.Text = "Please select a file first."
            End If


            If fileUpload8.HasFile Then
                Dim fileExt As String = System.IO.Path.GetExtension(fileUpload8.FileName).ToLower()
                If fileExt = ".jpeg" Or fileExt = ".jpg" Or fileExt = ".png" Or fileExt = ".gif" Or fileExt = ".webp" Or fileExt = ".raw" Or fileExt = ".heif" Then
                    'Dim savePath As String = Server.MapPath("~/UploadedFiles/")
                    Dim savePath As String = folderPath
                    Dim fileName As String = System.IO.Path.GetFileName(fileUpload8.FileName)
                    ' #Original
                    'savePath += fileName

                    If Right(fileName, 5) = ".jpeg" Then
                        Extension = Right(fileName, 5)
                    ElseIf Right(fileName, 5) = ".webp" Then
                        Extension = Right(fileName, 5)
                    ElseIf Right(fileName, 5) = ".heif" Then
                        Extension = Right(fileName, 5)
                    Else
                        Extension = Right(fileName, 4)
                    End If

                    NewFileNameExtension = Replace(StrTextModel, " ", "_") & "_(" & Replace(StrTextBrand, " ", "_") & ")_" & StrTextColor & "_Pic8_" & Replace(StrTextName, " ", "_") & "_" & formattedTime & "_8" & Extension
                    savePath += NewFileNameExtension

                    'Response.Write(savePath & "<br>" & NewFileNameExtension)

                    Try
                        fileUpload8.SaveAs(savePath)
                        Label8.Text = "File uploaded successfully!"
                        Tom_Pic_Finish = Tom_Pic_Finish + 1

                        Image8.Visible = 1
                        Image8.ImageUrl = URLPath & NewFileNameExtension
                    Catch ex As Exception
                        Label8.Text = "Error uploading file: " & ex.Message
                    End Try
                Else
                    Label8.Text = "Only Picture files are allowed."
                End If
            Else
                Label8.Text = "Please select a file first."
            End If


            If fileUpload9.HasFile Then
                Dim fileExt As String = System.IO.Path.GetExtension(fileUpload9.FileName).ToLower()
                If fileExt = ".jpeg" Or fileExt = ".jpg" Or fileExt = ".png" Or fileExt = ".gif" Or fileExt = ".webp" Or fileExt = ".raw" Or fileExt = ".heif" Then
                    'Dim savePath As String = Server.MapPath("~/UploadedFiles/")
                    Dim savePath As String = folderPath
                    Dim fileName As String = System.IO.Path.GetFileName(fileUpload9.FileName)
                    ' #Original
                    'savePath += fileName

                    If Right(fileName, 5) = ".jpeg" Then
                        Extension = Right(fileName, 5)
                    ElseIf Right(fileName, 5) = ".webp" Then
                        Extension = Right(fileName, 5)
                    ElseIf Right(fileName, 5) = ".heif" Then
                        Extension = Right(fileName, 5)
                    Else
                        Extension = Right(fileName, 4)
                    End If

                    NewFileNameExtension = Replace(StrTextModel, " ", "_") & "_(" & Replace(StrTextBrand, " ", "_") & ")_" & StrTextColor & "_Pic9_" & Replace(StrTextName, " ", "_") & "_" & formattedTime & "_9" & Extension
                    savePath += NewFileNameExtension

                    'Response.Write(savePath & "<br>" & NewFileNameExtension)

                    Try
                        fileUpload9.SaveAs(savePath)
                        Label9.Text = "File uploaded successfully!"
                        Tom_Pic_Finish = Tom_Pic_Finish + 1

                        Image9.Visible = 1
                        Image9.ImageUrl = URLPath & NewFileNameExtension
                    Catch ex As Exception
                        Label9.Text = "Error uploading file: " & ex.Message
                    End Try
                Else
                    Label9.Text = "Only Picture files are allowed."
                End If
            Else
                Label9.Text = "Please select a file first."
            End If


            If fileUpload10.HasFile Then
                Dim fileExt As String = System.IO.Path.GetExtension(fileUpload10.FileName).ToLower()
                If fileExt = ".jpeg" Or fileExt = ".jpg" Or fileExt = ".png" Or fileExt = ".gif" Or fileExt = ".webp" Or fileExt = ".raw" Or fileExt = ".heif" Then
                    'Dim savePath As String = Server.MapPath("~/UploadedFiles/")
                    Dim savePath As String = folderPath
                    Dim fileName As String = System.IO.Path.GetFileName(fileUpload10.FileName)
                    ' #Original
                    'savePath += fileName

                    If Right(fileName, 5) = ".jpeg" Then
                        Extension = Right(fileName, 5)
                    ElseIf Right(fileName, 5) = ".webp" Then
                        Extension = Right(fileName, 5)
                    ElseIf Right(fileName, 5) = ".heif" Then
                        Extension = Right(fileName, 5)
                    Else
                        Extension = Right(fileName, 4)
                    End If

                    NewFileNameExtension = Replace(StrTextModel, " ", "_") & "_(" & Replace(StrTextBrand, " ", "_") & ")_" & StrTextColor & "_Pic10_" & Replace(StrTextName, " ", "_") & "_" & formattedTime & "_10" & Extension
                    savePath += NewFileNameExtension

                    'Response.Write(savePath & "<br>" & NewFileNameExtension)

                    Try
                        fileUpload10.SaveAs(savePath)
                        Label10.Text = "File uploaded successfully!"
                        Tom_Pic_Finish = Tom_Pic_Finish + 1

                        Image10.Visible = 1
                        Image10.ImageUrl = URLPath & NewFileNameExtension
                    Catch ex As Exception
                        Label10.Text = "Error uploading file: " & ex.Message
                    End Try
                Else
                    Label10.Text = "Only Picture files are allowed."
                End If
            Else
                Label10.Text = "Please select a file first."
            End If





            ' VDO

            If fileUpload11.HasFile Then
                Dim fileExt As String = System.IO.Path.GetExtension(fileUpload11.FileName).ToLower()
                If fileExt = ".mov" Or fileExt = ".mp4" Or fileExt = ".3gp" Or fileExt = ".mkv" Or fileExt = ".avi" Or fileExt = ".flv" Or fileExt = ".webm" Then
                    'Dim savePath As String = Server.MapPath("~/UploadedFiles/")
                    Dim savePath As String = folderPath
                    Dim fileName As String = System.IO.Path.GetFileName(fileUpload11.FileName)
                    ' #Original
                    'savePath += fileName

                    If Right(fileName, 5) = ".webm" Then
                        Extension = Right(fileName, 5)
                    Else
                        Extension = Right(fileName, 4)
                    End If

                    NewFileNameExtension = Replace(StrTextModel, " ", "_") & "_(" & Replace(StrTextBrand, " ", "_") & ")_" & StrTextColor & "_Vdo1_" & Replace(StrTextName, " ", "_") & "_" & formattedTime & "_11" & Extension
                    savePath += NewFileNameExtension

                    'Response.Write(savePath & "<br>" & NewFileNameExtension)

                    Try
                        fileUpload11.SaveAs(savePath)
                        Label11.Text = "File uploaded successfully!"
                        Tom_Vdo_Finish = Tom_Vdo_Finish + 1

                        Image11.Visible = 1
                        Image11.ImageUrl = URLPath & NewFileNameExtension
                    Catch ex As Exception
                        Label11.Text = "Error uploading file: " & ex.Message
                    End Try
                Else
                    Label11.Text = "Only Video files are allowed."
                End If
            Else
                Label11.Text = "Please select a file first."
            End If


            If fileUpload12.HasFile Then
                Dim fileExt As String = System.IO.Path.GetExtension(fileUpload12.FileName).ToLower()
                If fileExt = ".mov" Or fileExt = ".mp4" Or fileExt = ".3gp" Or fileExt = ".mkv" Or fileExt = ".avi" Or fileExt = ".flv" Or fileExt = ".webm" Then
                    'Dim savePath As String = Server.MapPath("~/UploadedFiles/")
                    Dim savePath As String = folderPath
                    Dim fileName As String = System.IO.Path.GetFileName(fileUpload12.FileName)
                    ' #Original
                    'savePath += fileName

                    If Right(fileName, 5) = ".webm" Then
                        Extension = Right(fileName, 5)
                    Else
                        Extension = Right(fileName, 4)
                    End If

                    NewFileNameExtension = Replace(StrTextModel, " ", "_") & "_(" & Replace(StrTextBrand, " ", "_") & ")_" & StrTextColor & "_Vdo2_" & Replace(StrTextName, " ", "_") & "_" & formattedTime & "_12" & Extension
                    savePath += NewFileNameExtension

                    'Response.Write(savePath & "<br>" & NewFileNameExtension)

                    Try
                        fileUpload12.SaveAs(savePath)
                        Label12.Text = "File uploaded successfully!"
                        Tom_Vdo_Finish = Tom_Vdo_Finish + 1

                        Image12.Visible = 1
                        Image12.ImageUrl = URLPath & NewFileNameExtension
                    Catch ex As Exception
                        Label12.Text = "Error uploading file: " & ex.Message
                    End Try
                Else
                    Label12.Text = "Only Video files are allowed."
                End If
            Else
                Label12.Text = "Please select a file first."
            End If


            If Tom_Pic_Finish > 4 And Tom_Vdo_Finish = 0 Then
                errormsg = "Pic upload สมบูรณ์ครบ 5 ภาพ+ แต่ Vdo ยังไม่สมบูรณ์ ลองทำใหม่ เฉพาะ vdo"
            ElseIf Tom_Vdo_Finish > 0 And Tom_Pic_Finish < 5 Then
                errormsg = "Vdo upload สมบูรณ์ 1 vdo+ แต่ Pic ยังไม่สมบูรณ์ ลองทำใหม่ เฉพาะ pic"
            ElseIf Tom_Pic_Finish > 4 And Tom_Vdo_Finish > 0 Then
                errormsg = "Vdo 1 วิดีโอ และ Pic 5 ภาพ upload สมบูรณ์"

            ElseIf Tom_Pic_Finish > 5 And Tom_Vdo_Finish > 1 Then
                errormsg = "Vdo 2 วิดีโอ และ Pic มากกว่า 5 ภาพ upload สมบูรณ์. ขอบคุณมากนะครับ"

            ElseIf Tom_Pic_Finish > 4 And Tom_Vdo_Finish > 1 Then
                errormsg = "Vdo 2 วิดีโอ และ Pic 5 ภาพ upload สมบูรณ์"

            End If


        End If


    End Sub


End Class