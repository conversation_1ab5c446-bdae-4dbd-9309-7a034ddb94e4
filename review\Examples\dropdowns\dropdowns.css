.dropdown-item-danger {
  color: var(--bs-red);
}
.dropdown-item-danger:hover,
.dropdown-item-danger:focus {
  color: #fff;
  background-color: var(--bs-red);
}
.dropdown-item-danger.active {
  background-color: var(--bs-red);
}

.btn-hover-light {
  color: var(--bs-body-color);
  background-color: var(--bs-body-bg);
}
.btn-hover-light:hover,
.btn-hover-light:focus {
  color: var(--bs-link-hover-color);
  background-color: var(--bs-tertiary-bg);
}

.cal-month,
.cal-days,
.cal-weekdays {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  align-items: center;
}
.cal-month-name {
  grid-column-start: 2;
  grid-column-end: 7;
  text-align: center;
}
.cal-weekday,
.cal-btn {
  display: flex;
  flex-shrink: 0;
  align-items: center;
  justify-content: center;
  height: 3rem;
  padding: 0;
}
.cal-btn:not([disabled]) {
  font-weight: 500;
  color: var(--bs-emphasis-color);
}
.cal-btn:hover,
.cal-btn:focus {
  background-color: var(--bs-secondary-bg);
}
.cal-btn[disabled] {
  border: 0;
  opacity: .5;
}

.w-220px {
  width: 220px;
}

.w-280px {
  width: 280px;
}

.w-340px {
  width: 340px;
}

.opacity-10 {
  opacity: .1;
}
