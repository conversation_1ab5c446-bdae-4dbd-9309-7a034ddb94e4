<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Book Catalog</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 0;
            background-color: #f0f0f0;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        .book-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
            gap: 20px;
        }
        .book-item {
            background-color: #fff;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            transition: transform 0.3s ease;
            cursor: pointer;
        }
        .book-item:hover {
            transform: translateY(-5px);
        }
        .book-cover {
            width: 100%;
            height: 280px;
            background-size: cover;
            background-position: center;
            border-radius: 8px 8px 0 0;
        }
        .book-info {
            padding: 15px;
        }
        .book-title {
            margin: 0;
            font-size: 18px;
            color: #333;
        }
        .book-author {
            margin: 5px 0 0;
            font-size: 14px;
            color: #666;
        }
        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.8);
        }
        .modal-content {
            position: relative;
            width: 80%;
            max-width: 800px;
            margin: 50px auto;
            background-color: #fff;
            border-radius: 8px;
        }
        .close-btn {
            position: absolute;
            top: 10px;
            right: 20px;
            font-size: 30px;
            color: #fff;
            cursor: pointer;
        }
        .book-viewer {
            width: 100%;
            height: 600px;
            border: none;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Book Catalog</h1>
        <div class="book-grid" id="bookGrid">
            <!-- Books will be dynamically added here -->
        </div>
    </div>

    <div id="bookModal" class="modal">
        <span class="close-btn" onclick="closeModal()">&times;</span>
        <div class="modal-content">
            <iframe id="bookViewer" class="book-viewer" src=""></iframe>
        </div>
    </div>

    <script>
        // Sample book data (you would typically load this from your server)
        const books = [
            { id: 1, title: "Book 1", author: "Author 1", cover: "https://www.amibigeye.com/album/witcheslens/book1/pages/1-large.jpg", url: "photo-album-viewer.html" },
            { id: 2, title: "Book 2", author: "Author 2", cover: "https://via.placeholder.com/200x280?text=Book+2", url: "book2.html" },
            { id: 3, title: "Book 3", author: "Author 3", cover: "https://via.placeholder.com/200x280?text=Book+3", url: "book3.html" },
            // Add more books as needed
        ];

        function renderBooks() {
            const bookGrid = document.getElementById('bookGrid');
            books.forEach(book => {
                const bookElement = document.createElement('div');
                bookElement.className = 'book-item';
                bookElement.innerHTML = `
                    <div class="book-cover" style="background-image: url('${book.cover}')"></div>
                    <div class="book-info">
                        <h3 class="book-title">${book.title}</h3>
                        <p class="book-author">${book.author}</p>
                    </div>
                `;
                bookElement.onclick = () => openBook(book.url);
                bookGrid.appendChild(bookElement);
            });
        }

        function openBook(url) {
            const modal = document.getElementById('bookModal');
            const viewer = document.getElementById('bookViewer');
            viewer.src = url;
            modal.style.display = 'block';
        }

        function closeModal() {
            const modal = document.getElementById('bookModal');
            modal.style.display = 'none';
        }

        // Initialize the book grid
        renderBooks();
    </script>
</body>
</html>
