{"CodeKitInfo": "This is a CodeKit 2.x project configuration file. It is designed to sync project settings across multiple machines. MODIFYING THE CONTENTS OF THIS FILE IS A POOR LIFE DECISION. If you do so, you will likely cause CodeKit to crash. This file is not useful unless accompanied by the project that created it in CodeKit 2. This file is not backwards-compatible with CodeKit 1.x. For more information, see: http://incident57.com/codekit", "creatorBuild": "18493", "files": {"/404.html": {"fileType": 8192, "ignore": 1, "ignoreWasSetByUser": 0, "inputAbbreviatedPath": "/404.html", "outputAbbreviatedPath": "No Output Path", "outputPathIsOutsideProject": 0, "outputPathIsSetByUser": 0}, "/assets/images/404.png": {"fileType": 32768, "ignore": 0, "ignoreWasSetByUser": 0, "initialSize": 91842, "inputAbbreviatedPath": "/assets/images/404.png", "outputAbbreviatedPath": "/assets/images/404.png", "outputPathIsOutsideProject": 0, "outputPathIsSetByUser": 0, "processed": 0}, "/assets/images/about-1.png": {"fileType": 32768, "ignore": 0, "ignoreWasSetByUser": 0, "initialSize": 39660, "inputAbbreviatedPath": "/assets/images/about-1.png", "outputAbbreviatedPath": "/assets/images/about-1.png", "outputPathIsOutsideProject": 0, "outputPathIsSetByUser": 0, "processed": 0}, "/assets/images/about-2.png": {"fileType": 32768, "ignore": 0, "ignoreWasSetByUser": 0, "initialSize": 29259, "inputAbbreviatedPath": "/assets/images/about-2.png", "outputAbbreviatedPath": "/assets/images/about-2.png", "outputPathIsOutsideProject": 0, "outputPathIsSetByUser": 0, "processed": 0}, "/assets/images/about-3.png": {"fileType": 32768, "ignore": 0, "ignoreWasSetByUser": 0, "initialSize": 39846, "inputAbbreviatedPath": "/assets/images/about-3.png", "outputAbbreviatedPath": "/assets/images/about-3.png", "outputPathIsOutsideProject": 0, "outputPathIsSetByUser": 0, "processed": 0}, "/assets/images/arrow-blue.png": {"fileType": 32768, "ignore": 0, "ignoreWasSetByUser": 0, "initialSize": 329, "inputAbbreviatedPath": "/assets/images/arrow-blue.png", "outputAbbreviatedPath": "/assets/images/arrow-blue.png", "outputPathIsOutsideProject": 0, "outputPathIsSetByUser": 0, "processed": 0}, "/assets/images/arrow-blues.png": {"fileType": 32768, "ignore": 0, "ignoreWasSetByUser": 0, "initialSize": 918, "inputAbbreviatedPath": "/assets/images/arrow-blues.png", "outputAbbreviatedPath": "/assets/images/arrow-blues.png", "outputPathIsOutsideProject": 0, "outputPathIsSetByUser": 0, "processed": 0}, "/assets/images/arrow-green.png": {"fileType": 32768, "ignore": 0, "ignoreWasSetByUser": 0, "initialSize": 1467, "inputAbbreviatedPath": "/assets/images/arrow-green.png", "outputAbbreviatedPath": "/assets/images/arrow-green.png", "outputPathIsOutsideProject": 0, "outputPathIsSetByUser": 0, "processed": 0}, "/assets/images/arrow-grey.png": {"fileType": 32768, "ignore": 0, "ignoreWasSetByUser": 0, "initialSize": 239, "inputAbbreviatedPath": "/assets/images/arrow-grey.png", "outputAbbreviatedPath": "/assets/images/arrow-grey.png", "outputPathIsOutsideProject": 0, "outputPathIsSetByUser": 0, "processed": 0}, "/assets/images/arrow-greys.png": {"fileType": 32768, "ignore": 0, "ignoreWasSetByUser": 0, "initialSize": 310, "inputAbbreviatedPath": "/assets/images/arrow-greys.png", "outputAbbreviatedPath": "/assets/images/arrow-greys.png", "outputPathIsOutsideProject": 0, "outputPathIsSetByUser": 0, "processed": 0}, "/assets/images/arrow-lgreen.png": {"fileType": 32768, "ignore": 0, "ignoreWasSetByUser": 0, "initialSize": 327, "inputAbbreviatedPath": "/assets/images/arrow-lgreen.png", "outputAbbreviatedPath": "/assets/images/arrow-lgreen.png", "outputPathIsOutsideProject": 0, "outputPathIsSetByUser": 0, "processed": 0}, "/assets/images/arrow-orange.png": {"fileType": 32768, "ignore": 0, "ignoreWasSetByUser": 0, "initialSize": 329, "inputAbbreviatedPath": "/assets/images/arrow-orange.png", "outputAbbreviatedPath": "/assets/images/arrow-orange.png", "outputPathIsOutsideProject": 0, "outputPathIsSetByUser": 0, "processed": 0}, "/assets/images/arrow-oranges.png": {"fileType": 32768, "ignore": 0, "ignoreWasSetByUser": 0, "initialSize": 962, "inputAbbreviatedPath": "/assets/images/arrow-oranges.png", "outputAbbreviatedPath": "/assets/images/arrow-oranges.png", "outputPathIsOutsideProject": 0, "outputPathIsSetByUser": 0, "processed": 0}, "/assets/images/arrow-pink.png": {"fileType": 32768, "ignore": 0, "ignoreWasSetByUser": 0, "initialSize": 324, "inputAbbreviatedPath": "/assets/images/arrow-pink.png", "outputAbbreviatedPath": "/assets/images/arrow-pink.png", "outputPathIsOutsideProject": 0, "outputPathIsSetByUser": 0, "processed": 0}, "/assets/images/arrow-purple.png": {"fileType": 32768, "ignore": 0, "ignoreWasSetByUser": 0, "initialSize": 718, "inputAbbreviatedPath": "/assets/images/arrow-purple.png", "outputAbbreviatedPath": "/assets/images/arrow-purple.png", "outputPathIsOutsideProject": 0, "outputPathIsSetByUser": 0, "processed": 0}, "/assets/images/arrow-top.png": {"fileType": 32768, "ignore": 0, "ignoreWasSetByUser": 0, "initialSize": 180, "inputAbbreviatedPath": "/assets/images/arrow-top.png", "outputAbbreviatedPath": "/assets/images/arrow-top.png", "outputPathIsOutsideProject": 0, "outputPathIsSetByUser": 0, "processed": 0}, "/assets/images/arrow-violet.png": {"fileType": 32768, "ignore": 0, "ignoreWasSetByUser": 0, "initialSize": 320, "inputAbbreviatedPath": "/assets/images/arrow-violet.png", "outputAbbreviatedPath": "/assets/images/arrow-violet.png", "outputPathIsOutsideProject": 0, "outputPathIsSetByUser": 0, "processed": 0}, "/assets/images/arrow.png": {"fileType": 32768, "ignore": 0, "ignoreWasSetByUser": 0, "initialSize": 182, "inputAbbreviatedPath": "/assets/images/arrow.png", "outputAbbreviatedPath": "/assets/images/arrow.png", "outputPathIsOutsideProject": 0, "outputPathIsSetByUser": 0, "processed": 0}, "/assets/images/background-cake.png": {"fileType": 32768, "ignore": 0, "ignoreWasSetByUser": 0, "initialSize": 9967, "inputAbbreviatedPath": "/assets/images/background-cake.png", "outputAbbreviatedPath": "/assets/images/background-cake.png", "outputPathIsOutsideProject": 0, "outputPathIsSetByUser": 0, "processed": 0}, "/assets/images/cake-blue.png": {"fileType": 32768, "ignore": 0, "ignoreWasSetByUser": 0, "initialSize": 17049, "inputAbbreviatedPath": "/assets/images/cake-blue.png", "outputAbbreviatedPath": "/assets/images/cake-blue.png", "outputPathIsOutsideProject": 0, "outputPathIsSetByUser": 0, "processed": 0}, "/assets/images/cake-five.png": {"fileType": 32768, "ignore": 0, "ignoreWasSetByUser": 0, "initialSize": 32954, "inputAbbreviatedPath": "/assets/images/cake-five.png", "outputAbbreviatedPath": "/assets/images/cake-five.png", "outputPathIsOutsideProject": 0, "outputPathIsSetByUser": 0, "processed": 0}, "/assets/images/cake-four-buy.png": {"fileType": 32768, "ignore": 0, "ignoreWasSetByUser": 0, "initialSize": 31149, "inputAbbreviatedPath": "/assets/images/cake-four-buy.png", "outputAbbreviatedPath": "/assets/images/cake-four-buy.png", "outputPathIsOutsideProject": 0, "outputPathIsSetByUser": 0, "processed": 0}, "/assets/images/cake-four.png": {"fileType": 32768, "ignore": 0, "ignoreWasSetByUser": 0, "initialSize": 30913, "inputAbbreviatedPath": "/assets/images/cake-four.png", "outputAbbreviatedPath": "/assets/images/cake-four.png", "outputPathIsOutsideProject": 0, "outputPathIsSetByUser": 0, "processed": 0}, "/assets/images/cake-one-buy.png": {"fileType": 32768, "ignore": 0, "ignoreWasSetByUser": 0, "initialSize": 38831, "inputAbbreviatedPath": "/assets/images/cake-one-buy.png", "outputAbbreviatedPath": "/assets/images/cake-one-buy.png", "outputPathIsOutsideProject": 0, "outputPathIsSetByUser": 0, "processed": 0}, "/assets/images/cake-one.png": {"fileType": 32768, "ignore": 0, "ignoreWasSetByUser": 0, "initialSize": 31123, "inputAbbreviatedPath": "/assets/images/cake-one.png", "outputAbbreviatedPath": "/assets/images/cake-one.png", "outputPathIsOutsideProject": 0, "outputPathIsSetByUser": 0, "processed": 0}, "/assets/images/cake-orange.png": {"fileType": 32768, "ignore": 0, "ignoreWasSetByUser": 0, "initialSize": 17063, "inputAbbreviatedPath": "/assets/images/cake-orange.png", "outputAbbreviatedPath": "/assets/images/cake-orange.png", "outputPathIsOutsideProject": 0, "outputPathIsSetByUser": 0, "processed": 0}, "/assets/images/cake-pink.png": {"fileType": 32768, "ignore": 0, "ignoreWasSetByUser": 0, "initialSize": 674, "inputAbbreviatedPath": "/assets/images/cake-pink.png", "outputAbbreviatedPath": "/assets/images/cake-pink.png", "outputPathIsOutsideProject": 0, "outputPathIsSetByUser": 0, "processed": 0}, "/assets/images/cake-purple.png": {"fileType": 32768, "ignore": 0, "ignoreWasSetByUser": 0, "initialSize": 649, "inputAbbreviatedPath": "/assets/images/cake-purple.png", "outputAbbreviatedPath": "/assets/images/cake-purple.png", "outputPathIsOutsideProject": 0, "outputPathIsSetByUser": 0, "processed": 0}, "/assets/images/cake-red.png": {"fileType": 32768, "ignore": 0, "ignoreWasSetByUser": 0, "initialSize": 16612, "inputAbbreviatedPath": "/assets/images/cake-red.png", "outputAbbreviatedPath": "/assets/images/cake-red.png", "outputPathIsOutsideProject": 0, "outputPathIsSetByUser": 0, "processed": 0}, "/assets/images/cake-three-buy.png": {"fileType": 32768, "ignore": 0, "ignoreWasSetByUser": 0, "initialSize": 32060, "inputAbbreviatedPath": "/assets/images/cake-three-buy.png", "outputAbbreviatedPath": "/assets/images/cake-three-buy.png", "outputPathIsOutsideProject": 0, "outputPathIsSetByUser": 0, "processed": 0}, "/assets/images/cake-three.png": {"fileType": 32768, "ignore": 0, "ignoreWasSetByUser": 0, "initialSize": 31225, "inputAbbreviatedPath": "/assets/images/cake-three.png", "outputAbbreviatedPath": "/assets/images/cake-three.png", "outputPathIsOutsideProject": 0, "outputPathIsSetByUser": 0, "processed": 0}, "/assets/images/cake-two-buy.png": {"fileType": 32768, "ignore": 0, "ignoreWasSetByUser": 0, "initialSize": 26414, "inputAbbreviatedPath": "/assets/images/cake-two-buy.png", "outputAbbreviatedPath": "/assets/images/cake-two-buy.png", "outputPathIsOutsideProject": 0, "outputPathIsSetByUser": 0, "processed": 0}, "/assets/images/cake-two.png": {"fileType": 32768, "ignore": 0, "ignoreWasSetByUser": 0, "initialSize": 27452, "inputAbbreviatedPath": "/assets/images/cake-two.png", "outputAbbreviatedPath": "/assets/images/cake-two.png", "outputPathIsOutsideProject": 0, "outputPathIsSetByUser": 0, "processed": 0}, "/assets/images/cake-wed.png": {"fileType": 32768, "ignore": 0, "ignoreWasSetByUser": 0, "initialSize": 53105, "inputAbbreviatedPath": "/assets/images/cake-wed.png", "outputAbbreviatedPath": "/assets/images/cake-wed.png", "outputPathIsOutsideProject": 0, "outputPathIsSetByUser": 0, "processed": 0}, "/assets/images/cake-white-lg.png": {"fileType": 32768, "ignore": 0, "ignoreWasSetByUser": 0, "initialSize": 1099, "inputAbbreviatedPath": "/assets/images/cake-white-lg.png", "outputAbbreviatedPath": "/assets/images/cake-white-lg.png", "outputPathIsOutsideProject": 0, "outputPathIsSetByUser": 0, "processed": 0}, "/assets/images/cake-white.png": {"fileType": 32768, "ignore": 0, "ignoreWasSetByUser": 0, "initialSize": 724, "inputAbbreviatedPath": "/assets/images/cake-white.png", "outputAbbreviatedPath": "/assets/images/cake-white.png", "outputPathIsOutsideProject": 0, "outputPathIsSetByUser": 0, "processed": 0}, "/assets/images/cakes-co.jpg": {"fileType": 16384, "ignore": 0, "ignoreWasSetByUser": 0, "initialSize": 15093, "inputAbbreviatedPath": "/assets/images/cakes-co.jpg", "outputAbbreviatedPath": "/assets/images/cakes-co.jpg", "outputPathIsOutsideProject": 0, "outputPathIsSetByUser": 0, "processed": 0}, "/assets/images/cakes-co1.jpg": {"fileType": 16384, "ignore": 0, "ignoreWasSetByUser": 0, "initialSize": 33294, "inputAbbreviatedPath": "/assets/images/cakes-co1.jpg", "outputAbbreviatedPath": "/assets/images/cakes-co1.jpg", "outputPathIsOutsideProject": 0, "outputPathIsSetByUser": 0, "processed": 0}, "/assets/images/cakes-co2.jpg": {"fileType": 16384, "ignore": 0, "ignoreWasSetByUser": 0, "initialSize": 21509, "inputAbbreviatedPath": "/assets/images/cakes-co2.jpg", "outputAbbreviatedPath": "/assets/images/cakes-co2.jpg", "outputPathIsOutsideProject": 0, "outputPathIsSetByUser": 0, "processed": 0}, "/assets/images/client.png": {"fileType": 32768, "ignore": 0, "ignoreWasSetByUser": 0, "initialSize": 14374, "inputAbbreviatedPath": "/assets/images/client.png", "outputAbbreviatedPath": "/assets/images/client.png", "outputPathIsOutsideProject": 0, "outputPathIsSetByUser": 0, "processed": 0}, "/assets/images/dn.png": {"fileType": 32768, "ignore": 0, "ignoreWasSetByUser": 0, "initialSize": 514, "inputAbbreviatedPath": "/assets/images/dn.png", "outputAbbreviatedPath": "/assets/images/dn.png", "outputPathIsOutsideProject": 0, "outputPathIsSetByUser": 0, "processed": 0}, "/assets/images/dot-blue-1.png": {"fileType": 32768, "ignore": 0, "ignoreWasSetByUser": 0, "initialSize": 106, "inputAbbreviatedPath": "/assets/images/dot-blue-1.png", "outputAbbreviatedPath": "/assets/images/dot-blue-1.png", "outputPathIsOutsideProject": 0, "outputPathIsSetByUser": 0, "processed": 0}, "/assets/images/dot-blue.png": {"fileType": 32768, "ignore": 0, "ignoreWasSetByUser": 0, "initialSize": 226, "inputAbbreviatedPath": "/assets/images/dot-blue.png", "outputAbbreviatedPath": "/assets/images/dot-blue.png", "outputPathIsOutsideProject": 0, "outputPathIsSetByUser": 0, "processed": 0}, "/assets/images/dot-blues.png": {"fileType": 32768, "ignore": 0, "ignoreWasSetByUser": 0, "initialSize": 138, "inputAbbreviatedPath": "/assets/images/dot-blues.png", "outputAbbreviatedPath": "/assets/images/dot-blues.png", "outputPathIsOutsideProject": 0, "outputPathIsSetByUser": 0, "processed": 0}, "/assets/images/dot-green-1.png": {"fileType": 32768, "ignore": 0, "ignoreWasSetByUser": 0, "initialSize": 106, "inputAbbreviatedPath": "/assets/images/dot-green-1.png", "outputAbbreviatedPath": "/assets/images/dot-green-1.png", "outputPathIsOutsideProject": 0, "outputPathIsSetByUser": 0, "processed": 0}, "/assets/images/dot-green.png": {"fileType": 32768, "ignore": 0, "ignoreWasSetByUser": 0, "initialSize": 138, "inputAbbreviatedPath": "/assets/images/dot-green.png", "outputAbbreviatedPath": "/assets/images/dot-green.png", "outputPathIsOutsideProject": 0, "outputPathIsSetByUser": 0, "processed": 0}, "/assets/images/dot-orange-1.png": {"fileType": 32768, "ignore": 0, "ignoreWasSetByUser": 0, "initialSize": 109, "inputAbbreviatedPath": "/assets/images/dot-orange-1.png", "outputAbbreviatedPath": "/assets/images/dot-orange-1.png", "outputPathIsOutsideProject": 0, "outputPathIsSetByUser": 0, "processed": 0}, "/assets/images/dot-orange.png": {"fileType": 32768, "ignore": 0, "ignoreWasSetByUser": 0, "initialSize": 226, "inputAbbreviatedPath": "/assets/images/dot-orange.png", "outputAbbreviatedPath": "/assets/images/dot-orange.png", "outputPathIsOutsideProject": 0, "outputPathIsSetByUser": 0, "processed": 0}, "/assets/images/dot-oranges.png": {"fileType": 32768, "ignore": 0, "ignoreWasSetByUser": 0, "initialSize": 138, "inputAbbreviatedPath": "/assets/images/dot-oranges.png", "outputAbbreviatedPath": "/assets/images/dot-oranges.png", "outputPathIsOutsideProject": 0, "outputPathIsSetByUser": 0, "processed": 0}, "/assets/images/dot-pink.png": {"fileType": 32768, "ignore": 0, "ignoreWasSetByUser": 0, "initialSize": 224, "inputAbbreviatedPath": "/assets/images/dot-pink.png", "outputAbbreviatedPath": "/assets/images/dot-pink.png", "outputPathIsOutsideProject": 0, "outputPathIsSetByUser": 0, "processed": 0}, "/assets/images/dot-pinks.png": {"fileType": 32768, "ignore": 0, "ignoreWasSetByUser": 0, "initialSize": 138, "inputAbbreviatedPath": "/assets/images/dot-pinks.png", "outputAbbreviatedPath": "/assets/images/dot-pinks.png", "outputPathIsOutsideProject": 0, "outputPathIsSetByUser": 0, "processed": 0}, "/assets/images/dot-purple-1.png": {"fileType": 32768, "ignore": 0, "ignoreWasSetByUser": 0, "initialSize": 106, "inputAbbreviatedPath": "/assets/images/dot-purple-1.png", "outputAbbreviatedPath": "/assets/images/dot-purple-1.png", "outputPathIsOutsideProject": 0, "outputPathIsSetByUser": 0, "processed": 0}, "/assets/images/dot-purple.png": {"fileType": 32768, "ignore": 0, "ignoreWasSetByUser": 0, "initialSize": 240, "inputAbbreviatedPath": "/assets/images/dot-purple.png", "outputAbbreviatedPath": "/assets/images/dot-purple.png", "outputPathIsOutsideProject": 0, "outputPathIsSetByUser": 0, "processed": 0}, "/assets/images/dot-purples.png": {"fileType": 32768, "ignore": 0, "ignoreWasSetByUser": 0, "initialSize": 138, "inputAbbreviatedPath": "/assets/images/dot-purples.png", "outputAbbreviatedPath": "/assets/images/dot-purples.png", "outputPathIsOutsideProject": 0, "outputPathIsSetByUser": 0, "processed": 0}, "/assets/images/favicon-32x32.png": {"fileType": 32768, "ignore": 0, "ignoreWasSetByUser": 0, "initialSize": 376, "inputAbbreviatedPath": "/assets/images/favicon-32x32.png", "outputAbbreviatedPath": "/assets/images/favicon-32x32.png", "outputPathIsOutsideProject": 0, "outputPathIsSetByUser": 0, "processed": 0}, "/assets/images/gal-1.jpg": {"fileType": 16384, "ignore": 0, "ignoreWasSetByUser": 0, "initialSize": 33332, "inputAbbreviatedPath": "/assets/images/gal-1.jpg", "outputAbbreviatedPath": "/assets/images/gal-1.jpg", "outputPathIsOutsideProject": 0, "outputPathIsSetByUser": 0, "processed": 0}, "/assets/images/gal-2.jpg": {"fileType": 16384, "ignore": 0, "ignoreWasSetByUser": 0, "initialSize": 27110, "inputAbbreviatedPath": "/assets/images/gal-2.jpg", "outputAbbreviatedPath": "/assets/images/gal-2.jpg", "outputPathIsOutsideProject": 0, "outputPathIsSetByUser": 0, "processed": 0}, "/assets/images/gal-3.jpg": {"fileType": 16384, "ignore": 0, "ignoreWasSetByUser": 0, "initialSize": 28787, "inputAbbreviatedPath": "/assets/images/gal-3.jpg", "outputAbbreviatedPath": "/assets/images/gal-3.jpg", "outputPathIsOutsideProject": 0, "outputPathIsSetByUser": 0, "processed": 0}, "/assets/images/gal-4.jpg": {"fileType": 16384, "ignore": 0, "ignoreWasSetByUser": 0, "initialSize": 16162, "inputAbbreviatedPath": "/assets/images/gal-4.jpg", "outputAbbreviatedPath": "/assets/images/gal-4.jpg", "outputPathIsOutsideProject": 0, "outputPathIsSetByUser": 0, "processed": 0}, "/assets/images/gal-5.jpg": {"fileType": 16384, "ignore": 0, "ignoreWasSetByUser": 0, "initialSize": 22053, "inputAbbreviatedPath": "/assets/images/gal-5.jpg", "outputAbbreviatedPath": "/assets/images/gal-5.jpg", "outputPathIsOutsideProject": 0, "outputPathIsSetByUser": 0, "processed": 0}, "/assets/images/gal-6.jpg": {"fileType": 16384, "ignore": 0, "ignoreWasSetByUser": 0, "initialSize": 13918, "inputAbbreviatedPath": "/assets/images/gal-6.jpg", "outputAbbreviatedPath": "/assets/images/gal-6.jpg", "outputPathIsOutsideProject": 0, "outputPathIsSetByUser": 0, "processed": 0}, "/assets/images/gallery1.jpg": {"fileType": 16384, "ignore": 0, "ignoreWasSetByUser": 0, "initialSize": 6093, "inputAbbreviatedPath": "/assets/images/gallery1.jpg", "outputAbbreviatedPath": "/assets/images/gallery1.jpg", "outputPathIsOutsideProject": 0, "outputPathIsSetByUser": 0, "processed": 0}, "/assets/images/gel-1.jpg": {"fileType": 16384, "ignore": 0, "ignoreWasSetByUser": 0, "initialSize": 16108, "inputAbbreviatedPath": "/assets/images/gel-1.jpg", "outputAbbreviatedPath": "/assets/images/gel-1.jpg", "outputPathIsOutsideProject": 0, "outputPathIsSetByUser": 0, "processed": 0}, "/assets/images/gel-2.jpg": {"fileType": 16384, "ignore": 0, "ignoreWasSetByUser": 0, "initialSize": 22056, "inputAbbreviatedPath": "/assets/images/gel-2.jpg", "outputAbbreviatedPath": "/assets/images/gel-2.jpg", "outputPathIsOutsideProject": 0, "outputPathIsSetByUser": 0, "processed": 0}, "/assets/images/gel-3.jpg": {"fileType": 16384, "ignore": 0, "ignoreWasSetByUser": 0, "initialSize": 17720, "inputAbbreviatedPath": "/assets/images/gel-3.jpg", "outputAbbreviatedPath": "/assets/images/gel-3.jpg", "outputPathIsOutsideProject": 0, "outputPathIsSetByUser": 0, "processed": 0}, "/assets/images/gel-4.jpg": {"fileType": 16384, "ignore": 0, "ignoreWasSetByUser": 0, "initialSize": 12012, "inputAbbreviatedPath": "/assets/images/gel-4.jpg", "outputAbbreviatedPath": "/assets/images/gel-4.jpg", "outputPathIsOutsideProject": 0, "outputPathIsSetByUser": 0, "processed": 0}, "/assets/images/gel-5.jpg": {"fileType": 16384, "ignore": 0, "ignoreWasSetByUser": 0, "initialSize": 26071, "inputAbbreviatedPath": "/assets/images/gel-5.jpg", "outputAbbreviatedPath": "/assets/images/gel-5.jpg", "outputPathIsOutsideProject": 0, "outputPathIsSetByUser": 0, "processed": 0}, "/assets/images/gel-6.jpg": {"fileType": 16384, "ignore": 0, "ignoreWasSetByUser": 0, "initialSize": 39434, "inputAbbreviatedPath": "/assets/images/gel-6.jpg", "outputAbbreviatedPath": "/assets/images/gel-6.jpg", "outputPathIsOutsideProject": 0, "outputPathIsSetByUser": 0, "processed": 0}, "/assets/images/gel-7.jpg": {"fileType": 16384, "ignore": 0, "ignoreWasSetByUser": 0, "initialSize": 25187, "inputAbbreviatedPath": "/assets/images/gel-7.jpg", "outputAbbreviatedPath": "/assets/images/gel-7.jpg", "outputPathIsOutsideProject": 0, "outputPathIsSetByUser": 0, "processed": 0}, "/assets/images/gel-8.jpg": {"fileType": 16384, "ignore": 0, "ignoreWasSetByUser": 0, "initialSize": 34843, "inputAbbreviatedPath": "/assets/images/gel-8.jpg", "outputAbbreviatedPath": "/assets/images/gel-8.jpg", "outputPathIsOutsideProject": 0, "outputPathIsSetByUser": 0, "processed": 0}, "/assets/images/gel-9.jpg": {"fileType": 16384, "ignore": 0, "ignoreWasSetByUser": 0, "initialSize": 17628, "inputAbbreviatedPath": "/assets/images/gel-9.jpg", "outputAbbreviatedPath": "/assets/images/gel-9.jpg", "outputPathIsOutsideProject": 0, "outputPathIsSetByUser": 0, "processed": 0}, "/assets/images/green-s.jpg": {"fileType": 16384, "ignore": 0, "ignoreWasSetByUser": 0, "initialSize": 49883, "inputAbbreviatedPath": "/assets/images/green-s.jpg", "outputAbbreviatedPath": "/assets/images/green-s.jpg", "outputPathIsOutsideProject": 0, "outputPathIsSetByUser": 0, "processed": 0}, "/assets/images/ice-cream-cake.png": {"fileType": 32768, "ignore": 0, "ignoreWasSetByUser": 0, "initialSize": 26879, "inputAbbreviatedPath": "/assets/images/ice-cream-cake.png", "outputAbbreviatedPath": "/assets/images/ice-cream-cake.png", "outputPathIsOutsideProject": 0, "outputPathIsSetByUser": 0, "processed": 0}, "/assets/images/ice-cream.png": {"fileType": 32768, "ignore": 0, "ignoreWasSetByUser": 0, "initialSize": 29075, "inputAbbreviatedPath": "/assets/images/ice-cream.png", "outputAbbreviatedPath": "/assets/images/ice-cream.png", "outputPathIsOutsideProject": 0, "outputPathIsSetByUser": 0, "processed": 0}, "/assets/images/logo-100.png": {"fileType": 32768, "ignore": 0, "ignoreWasSetByUser": 0, "initialSize": 3059, "inputAbbreviatedPath": "/assets/images/logo-100.png", "outputAbbreviatedPath": "/assets/images/logo-100.png", "outputPathIsOutsideProject": 0, "outputPathIsSetByUser": 0, "processed": 0}, "/assets/images/logo-150.png": {"fileType": 32768, "ignore": 0, "ignoreWasSetByUser": 0, "initialSize": 4509, "inputAbbreviatedPath": "/assets/images/logo-150.png", "outputAbbreviatedPath": "/assets/images/logo-150.png", "outputPathIsOutsideProject": 0, "outputPathIsSetByUser": 0, "processed": 0}, "/assets/images/logo-white.png": {"fileType": 32768, "ignore": 0, "ignoreWasSetByUser": 0, "initialSize": 2634, "inputAbbreviatedPath": "/assets/images/logo-white.png", "outputAbbreviatedPath": "/assets/images/logo-white.png", "outputPathIsOutsideProject": 0, "outputPathIsSetByUser": 0, "processed": 0}, "/assets/images/logo.png": {"fileType": 32768, "ignore": 0, "ignoreWasSetByUser": 0, "initialSize": 4002, "inputAbbreviatedPath": "/assets/images/logo.png", "outputAbbreviatedPath": "/assets/images/logo.png", "outputPathIsOutsideProject": 0, "outputPathIsSetByUser": 0, "processed": 0}, "/assets/images/messes.png": {"fileType": 32768, "ignore": 0, "ignoreWasSetByUser": 0, "initialSize": 3328, "inputAbbreviatedPath": "/assets/images/messes.png", "outputAbbreviatedPath": "/assets/images/messes.png", "outputPathIsOutsideProject": 0, "outputPathIsSetByUser": 0, "processed": 0}, "/assets/images/price-blue.png": {"fileType": 32768, "ignore": 0, "ignoreWasSetByUser": 0, "initialSize": 7817, "inputAbbreviatedPath": "/assets/images/price-blue.png", "outputAbbreviatedPath": "/assets/images/price-blue.png", "outputPathIsOutsideProject": 0, "outputPathIsSetByUser": 0, "processed": 0}, "/assets/images/price-green.png": {"fileType": 32768, "ignore": 0, "ignoreWasSetByUser": 0, "initialSize": 7912, "inputAbbreviatedPath": "/assets/images/price-green.png", "outputAbbreviatedPath": "/assets/images/price-green.png", "outputPathIsOutsideProject": 0, "outputPathIsSetByUser": 0, "processed": 0}, "/assets/images/price-pink.png": {"fileType": 32768, "ignore": 0, "ignoreWasSetByUser": 0, "initialSize": 6945, "inputAbbreviatedPath": "/assets/images/price-pink.png", "outputAbbreviatedPath": "/assets/images/price-pink.png", "outputPathIsOutsideProject": 0, "outputPathIsSetByUser": 0, "processed": 0}, "/assets/images/price-purple.png": {"fileType": 32768, "ignore": 0, "ignoreWasSetByUser": 0, "initialSize": 7946, "inputAbbreviatedPath": "/assets/images/price-purple.png", "outputAbbreviatedPath": "/assets/images/price-purple.png", "outputPathIsOutsideProject": 0, "outputPathIsSetByUser": 0, "processed": 0}, "/assets/images/quote.png": {"fileType": 32768, "ignore": 0, "ignoreWasSetByUser": 0, "initialSize": 430, "inputAbbreviatedPath": "/assets/images/quote.png", "outputAbbreviatedPath": "/assets/images/quote.png", "outputPathIsOutsideProject": 0, "outputPathIsSetByUser": 0, "processed": 0}, "/assets/images/shop-cake1.jpg": {"fileType": 16384, "ignore": 0, "ignoreWasSetByUser": 0, "initialSize": 20210, "inputAbbreviatedPath": "/assets/images/shop-cake1.jpg", "outputAbbreviatedPath": "/assets/images/shop-cake1.jpg", "outputPathIsOutsideProject": 0, "outputPathIsSetByUser": 0, "processed": 0}, "/assets/images/shop-cake2.jpg": {"fileType": 16384, "ignore": 0, "ignoreWasSetByUser": 0, "initialSize": 13632, "inputAbbreviatedPath": "/assets/images/shop-cake2.jpg", "outputAbbreviatedPath": "/assets/images/shop-cake2.jpg", "outputPathIsOutsideProject": 0, "outputPathIsSetByUser": 0, "processed": 0}, "/assets/images/shop-cake3.jpg": {"fileType": 16384, "ignore": 0, "ignoreWasSetByUser": 0, "initialSize": 24939, "inputAbbreviatedPath": "/assets/images/shop-cake3.jpg", "outputAbbreviatedPath": "/assets/images/shop-cake3.jpg", "outputPathIsOutsideProject": 0, "outputPathIsSetByUser": 0, "processed": 0}, "/assets/images/star.png": {"fileType": 32768, "ignore": 0, "ignoreWasSetByUser": 0, "initialSize": 450, "inputAbbreviatedPath": "/assets/images/star.png", "outputAbbreviatedPath": "/assets/images/star.png", "outputPathIsOutsideProject": 0, "outputPathIsSetByUser": 0, "processed": 0}, "/assets/images/strips-pink.png": {"fileType": 32768, "ignore": 0, "ignoreWasSetByUser": 0, "initialSize": 715, "inputAbbreviatedPath": "/assets/images/strips-pink.png", "outputAbbreviatedPath": "/assets/images/strips-pink.png", "outputPathIsOutsideProject": 0, "outputPathIsSetByUser": 0, "processed": 0}, "/assets/images/tag-1.jpg": {"fileType": 16384, "ignore": 0, "ignoreWasSetByUser": 0, "initialSize": 4402, "inputAbbreviatedPath": "/assets/images/tag-1.jpg", "outputAbbreviatedPath": "/assets/images/tag-1.jpg", "outputPathIsOutsideProject": 0, "outputPathIsSetByUser": 0, "processed": 0}, "/assets/images/tag-2.jpg": {"fileType": 16384, "ignore": 0, "ignoreWasSetByUser": 0, "initialSize": 5056, "inputAbbreviatedPath": "/assets/images/tag-2.jpg", "outputAbbreviatedPath": "/assets/images/tag-2.jpg", "outputPathIsOutsideProject": 0, "outputPathIsSetByUser": 0, "processed": 0}, "/assets/images/tag-3.jpg": {"fileType": 16384, "ignore": 0, "ignoreWasSetByUser": 0, "initialSize": 5554, "inputAbbreviatedPath": "/assets/images/tag-3.jpg", "outputAbbreviatedPath": "/assets/images/tag-3.jpg", "outputPathIsOutsideProject": 0, "outputPathIsSetByUser": 0, "processed": 0}, "/assets/images/tag-4.jpg": {"fileType": 16384, "ignore": 0, "ignoreWasSetByUser": 0, "initialSize": 6001, "inputAbbreviatedPath": "/assets/images/tag-4.jpg", "outputAbbreviatedPath": "/assets/images/tag-4.jpg", "outputPathIsOutsideProject": 0, "outputPathIsSetByUser": 0, "processed": 0}, "/assets/images/tag-5.jpg": {"fileType": 16384, "ignore": 0, "ignoreWasSetByUser": 0, "initialSize": 3172, "inputAbbreviatedPath": "/assets/images/tag-5.jpg", "outputAbbreviatedPath": "/assets/images/tag-5.jpg", "outputPathIsOutsideProject": 0, "outputPathIsSetByUser": 0, "processed": 0}, "/assets/images/tag-6.jpg": {"fileType": 16384, "ignore": 0, "ignoreWasSetByUser": 0, "initialSize": 4417, "inputAbbreviatedPath": "/assets/images/tag-6.jpg", "outputAbbreviatedPath": "/assets/images/tag-6.jpg", "outputPathIsOutsideProject": 0, "outputPathIsSetByUser": 0, "processed": 0}, "/assets/images/tag-7.jpg": {"fileType": 16384, "ignore": 0, "ignoreWasSetByUser": 0, "initialSize": 3989, "inputAbbreviatedPath": "/assets/images/tag-7.jpg", "outputAbbreviatedPath": "/assets/images/tag-7.jpg", "outputPathIsOutsideProject": 0, "outputPathIsSetByUser": 0, "processed": 0}, "/assets/images/tag-8.jpg": {"fileType": 16384, "ignore": 0, "ignoreWasSetByUser": 0, "initialSize": 3146, "inputAbbreviatedPath": "/assets/images/tag-8.jpg", "outputAbbreviatedPath": "/assets/images/tag-8.jpg", "outputPathIsOutsideProject": 0, "outputPathIsSetByUser": 0, "processed": 0}, "/assets/javascripts/bootstrap.js": {"fileType": 64, "ignore": 0, "ignoreWasSetByUser": 0, "inputAbbreviatedPath": "/assets/javascripts/bootstrap.js", "outputAbbreviatedPath": "/assets/javascripts/min/bootstrap-min.js", "outputPathIsOutsideProject": 0, "outputPathIsSetByUser": 0, "outputStyle": 1, "syntaxCheckerStyle": 1}, "/assets/javascripts/classie.js": {"fileType": 64, "ignore": 0, "ignoreWasSetByUser": 0, "inputAbbreviatedPath": "/assets/javascripts/classie.js", "outputAbbreviatedPath": "/assets/javascripts/min/classie-min.js", "outputPathIsOutsideProject": 0, "outputPathIsSetByUser": 0, "outputStyle": 1, "syntaxCheckerStyle": 1}, "/assets/javascripts/custom.js": {"fileType": 64, "ignore": 0, "ignoreWasSetByUser": 0, "inputAbbreviatedPath": "/assets/javascripts/custom.js", "outputAbbreviatedPath": "/assets/javascripts/min/custom-min.js", "outputPathIsOutsideProject": 0, "outputPathIsSetByUser": 0, "outputStyle": 1, "syntaxCheckerStyle": 1}, "/assets/javascripts/fancybox/fancybox_overlay.png": {"fileType": 32768, "ignore": 0, "ignoreWasSetByUser": 0, "initialSize": 1003, "inputAbbreviatedPath": "/assets/javascripts/fancybox/fancybox_overlay.png", "outputAbbreviatedPath": "/assets/javascripts/fancybox/fancybox_overlay.png", "outputPathIsOutsideProject": 0, "outputPathIsSetByUser": 0, "processed": 0}, "/assets/javascripts/fancybox/fancybox_sprite.png": {"fileType": 32768, "ignore": 0, "ignoreWasSetByUser": 0, "initialSize": 1362, "inputAbbreviatedPath": "/assets/javascripts/fancybox/fancybox_sprite.png", "outputAbbreviatedPath": "/assets/javascripts/fancybox/fancybox_sprite.png", "outputPathIsOutsideProject": 0, "outputPathIsSetByUser": 0, "processed": 0}, "/assets/javascripts/fancybox/<EMAIL>": {"fileType": 32768, "ignore": 0, "ignoreWasSetByUser": 0, "initialSize": 6553, "inputAbbreviatedPath": "/assets/javascripts/fancybox/<EMAIL>", "outputAbbreviatedPath": "/assets/javascripts/fancybox/<EMAIL>", "outputPathIsOutsideProject": 0, "outputPathIsSetByUser": 0, "processed": 0}, "/assets/javascripts/fancybox/jquery.fancybox.css": {"fileType": 16, "ignore": 0, "ignoreWasSetByUser": 0, "inputAbbreviatedPath": "/assets/javascripts/fancybox/jquery.fancybox.css", "outputAbbreviatedPath": "No Output Path", "outputPathIsOutsideProject": 0, "outputPathIsSetByUser": 0}, "/assets/javascripts/fancybox/jquery.fancybox.pack.js": {"fileType": 64, "ignore": 0, "ignoreWasSetByUser": 0, "inputAbbreviatedPath": "/assets/javascripts/fancybox/jquery.fancybox.pack.js", "outputAbbreviatedPath": "/assets/javascripts/fancybox/min/jquery.fancybox.pack-min.js", "outputPathIsOutsideProject": 0, "outputPathIsSetByUser": 0, "outputStyle": 1, "syntaxCheckerStyle": 1}, "/assets/javascripts/jpreloader/js/jpreLoader.js": {"fileType": 64, "ignore": 0, "ignoreWasSetByUser": 0, "inputAbbreviatedPath": "/assets/javascripts/jpreloader/js/jpreLoader.js", "outputAbbreviatedPath": "/assets/javascripts/jpreloader/js/min/jpreLoader-min.js", "outputPathIsOutsideProject": 0, "outputPathIsSetByUser": 0, "outputStyle": 1, "syntaxCheckerStyle": 1}, "/assets/javascripts/jpreloader/js/jpreLoader.min.js": {"fileType": 64, "ignore": 0, "ignoreWasSetByUser": 0, "inputAbbreviatedPath": "/assets/javascripts/jpreloader/js/jpreLoader.min.js", "outputAbbreviatedPath": "/assets/javascripts/jpreloader/js/min/jpreLoader.min-min.js", "outputPathIsOutsideProject": 0, "outputPathIsSetByUser": 0, "outputStyle": 1, "syntaxCheckerStyle": 1}, "/assets/javascripts/jquery.js": {"fileType": 64, "ignore": 0, "ignoreWasSetByUser": 0, "inputAbbreviatedPath": "/assets/javascripts/jquery.js", "outputAbbreviatedPath": "/assets/javascripts/min/jquery-min.js", "outputPathIsOutsideProject": 0, "outputPathIsSetByUser": 0, "outputStyle": 1, "syntaxCheckerStyle": 1}, "/assets/javascripts/main.js": {"fileType": 64, "ignore": 0, "ignoreWasSetByUser": 0, "inputAbbreviatedPath": "/assets/javascripts/main.js", "outputAbbreviatedPath": "/assets/javascripts/min/main-min.js", "outputPathIsOutsideProject": 0, "outputPathIsSetByUser": 0, "outputStyle": 1, "syntaxCheckerStyle": 1}, "/assets/javascripts/min/custom-min.js": {"fileType": 64, "ignore": 1, "ignoreWasSetByUser": 0, "inputAbbreviatedPath": "/assets/javascripts/min/custom-min.js", "outputAbbreviatedPath": "/assets/javascripts/min/min/custom-min-min.js", "outputPathIsOutsideProject": 0, "outputPathIsSetByUser": 0, "outputStyle": 1, "syntaxCheckerStyle": 1}, "/assets/javascripts/min/jquery-min.js": {"fileType": 64, "ignore": 1, "ignoreWasSetByUser": 0, "inputAbbreviatedPath": "/assets/javascripts/min/jquery-min.js", "outputAbbreviatedPath": "/assets/javascripts/min/min/jquery-min-min.js", "outputPathIsOutsideProject": 0, "outputPathIsSetByUser": 0, "outputStyle": 1, "syntaxCheckerStyle": 1}, "/assets/javascripts/min/jquery.min-min.js": {"fileType": 64, "ignore": 0, "ignoreWasSetByUser": 0, "inputAbbreviatedPath": "/assets/javascripts/min/jquery.min-min.js", "outputAbbreviatedPath": "/assets/javascripts/min/min/jquery.min-min-min.js", "outputPathIsOutsideProject": 0, "outputPathIsSetByUser": 0, "outputStyle": 1, "syntaxCheckerStyle": 1}, "/assets/javascripts/min/main-min.js": {"fileType": 64, "ignore": 1, "ignoreWasSetByUser": 0, "inputAbbreviatedPath": "/assets/javascripts/min/main-min.js", "outputAbbreviatedPath": "/assets/javascripts/min/min/main-min-min.js", "outputPathIsOutsideProject": 0, "outputPathIsSetByUser": 0, "outputStyle": 1, "syntaxCheckerStyle": 1}, "/assets/javascripts/modernizr.custom.js": {"fileType": 64, "ignore": 0, "ignoreWasSetByUser": 0, "inputAbbreviatedPath": "/assets/javascripts/modernizr.custom.js", "outputAbbreviatedPath": "/assets/javascripts/min/modernizr.custom-min.js", "outputPathIsOutsideProject": 0, "outputPathIsSetByUser": 0, "outputStyle": 1, "syntaxCheckerStyle": 1}, "/assets/javascripts/npm.js": {"fileType": 64, "ignore": 0, "ignoreWasSetByUser": 0, "inputAbbreviatedPath": "/assets/javascripts/npm.js", "outputAbbreviatedPath": "/assets/javascripts/min/npm-min.js", "outputPathIsOutsideProject": 0, "outputPathIsSetByUser": 0, "outputStyle": 1, "syntaxCheckerStyle": 1}, "/assets/javascripts/pathLoader.js": {"fileType": 64, "ignore": 0, "ignoreWasSetByUser": 0, "inputAbbreviatedPath": "/assets/javascripts/pathLoader.js", "outputAbbreviatedPath": "/assets/javascripts/min/pathLoader-min.js", "outputPathIsOutsideProject": 0, "outputPathIsSetByUser": 0, "outputStyle": 1, "syntaxCheckerStyle": 1}, "/assets/javascripts/slick.js": {"fileType": 64, "ignore": 0, "ignoreWasSetByUser": 0, "inputAbbreviatedPath": "/assets/javascripts/slick.js", "outputAbbreviatedPath": "/assets/javascripts/min/slick-min.js", "outputPathIsOutsideProject": 0, "outputPathIsSetByUser": 0, "outputStyle": 1, "syntaxCheckerStyle": 1}, "/assets/javascripts/wow/min/wow-min.js": {"fileType": 64, "ignore": 1, "ignoreWasSetByUser": 0, "inputAbbreviatedPath": "/assets/javascripts/wow/min/wow-min.js", "outputAbbreviatedPath": "/assets/javascripts/wow/min/min/wow-min-min.js", "outputPathIsOutsideProject": 0, "outputPathIsSetByUser": 0, "outputStyle": 1, "syntaxCheckerStyle": 1}, "/assets/javascripts/wow/wow.js": {"fileType": 64, "ignore": 0, "ignoreWasSetByUser": 0, "inputAbbreviatedPath": "/assets/javascripts/wow/wow.js", "outputAbbreviatedPath": "/assets/javascripts/wow/min/wow-min.js", "outputPathIsOutsideProject": 0, "outputPathIsSetByUser": 0, "outputStyle": 1, "syntaxCheckerStyle": 1}, "/assets/javascripts/wow/wow.min.js": {"fileType": 64, "ignore": 0, "ignoreWasSetByUser": 0, "inputAbbreviatedPath": "/assets/javascripts/wow/wow.min.js", "outputAbbreviatedPath": "/assets/javascripts/wow/min/wow.min-min.js", "outputPathIsOutsideProject": 0, "outputPathIsSetByUser": 0, "outputStyle": 1, "syntaxCheckerStyle": 1}, "/assets/stylesheets/css/animate.css": {"fileType": 16, "ignore": 0, "ignoreWasSetByUser": 0, "inputAbbreviatedPath": "/assets/stylesheets/css/animate.css", "outputAbbreviatedPath": "No Output Path", "outputPathIsOutsideProject": 0, "outputPathIsSetByUser": 0}, "/assets/stylesheets/css/bootstrap.css": {"fileType": 16, "ignore": 0, "ignoreWasSetByUser": 0, "inputAbbreviatedPath": "/assets/stylesheets/css/bootstrap.css", "outputAbbreviatedPath": "No Output Path", "outputPathIsOutsideProject": 0, "outputPathIsSetByUser": 0}, "/assets/stylesheets/css/effect2.css": {"fileType": 16, "ignore": 0, "ignoreWasSetByUser": 0, "inputAbbreviatedPath": "/assets/stylesheets/css/effect2.css", "outputAbbreviatedPath": "No Output Path", "outputPathIsOutsideProject": 0, "outputPathIsSetByUser": 0}, "/assets/stylesheets/css/font-family.css": {"fileType": 16, "ignore": 1, "ignoreWasSetByUser": 0, "inputAbbreviatedPath": "/assets/stylesheets/css/font-family.css", "outputAbbreviatedPath": "No Output Path", "outputPathIsOutsideProject": 0, "outputPathIsSetByUser": 0}, "/assets/stylesheets/css/global.css": {"fileType": 16, "ignore": 1, "ignoreWasSetByUser": 0, "inputAbbreviatedPath": "/assets/stylesheets/css/global.css", "outputAbbreviatedPath": "No Output Path", "outputPathIsOutsideProject": 0, "outputPathIsSetByUser": 0}, "/assets/stylesheets/css/responsive.css": {"fileType": 16, "ignore": 0, "ignoreWasSetByUser": 0, "inputAbbreviatedPath": "/assets/stylesheets/css/responsive.css", "outputAbbreviatedPath": "No Output Path", "outputPathIsOutsideProject": 0, "outputPathIsSetByUser": 0}, "/assets/stylesheets/css/slick-theme.css": {"fileType": 16, "ignore": 0, "ignoreWasSetByUser": 0, "inputAbbreviatedPath": "/assets/stylesheets/css/slick-theme.css", "outputAbbreviatedPath": "No Output Path", "outputPathIsOutsideProject": 0, "outputPathIsSetByUser": 0}, "/assets/stylesheets/css/slick.css": {"fileType": 16, "ignore": 0, "ignoreWasSetByUser": 0, "inputAbbreviatedPath": "/assets/stylesheets/css/slick.css", "outputAbbreviatedPath": "No Output Path", "outputPathIsOutsideProject": 0, "outputPathIsSetByUser": 0}, "/assets/stylesheets/css/style.css": {"fileType": 16, "ignore": 1, "ignoreWasSetByUser": 0, "inputAbbreviatedPath": "/assets/stylesheets/css/style.css", "outputAbbreviatedPath": "No Output Path", "outputPathIsOutsideProject": 0, "outputPathIsSetByUser": 0}, "/assets/stylesheets/css/variable.css": {"fileType": 16, "ignore": 1, "ignoreWasSetByUser": 0, "inputAbbreviatedPath": "/assets/stylesheets/css/variable.css", "outputAbbreviatedPath": "No Output Path", "outputPathIsOutsideProject": 0, "outputPathIsSetByUser": 0}, "/assets/stylesheets/less/font-family.less": {"allowInsecureImports": 0, "createSourceMap": 0, "disableJavascript": 0, "fileType": 1, "ieCompatibility": 1, "ignore": 0, "ignoreWasSetByUser": 0, "inputAbbreviatedPath": "/assets/stylesheets/less/font-family.less", "outputAbbreviatedPath": "/assets/stylesheets/css/font-family.css", "outputPathIsOutsideProject": 0, "outputPathIsSetByUser": 0, "outputStyle": 0, "relativeURLS": 0, "shouldRunAutoprefixer": 0, "shouldRunBless": 0, "strictImports": 0, "strictMath": 0, "strictUnits": 0}, "/assets/stylesheets/less/global.less": {"allowInsecureImports": 0, "createSourceMap": 0, "disableJavascript": 0, "fileType": 1, "ieCompatibility": 1, "ignore": 0, "ignoreWasSetByUser": 0, "inputAbbreviatedPath": "/assets/stylesheets/less/global.less", "outputAbbreviatedPath": "/assets/stylesheets/css/global.css", "outputPathIsOutsideProject": 0, "outputPathIsSetByUser": 0, "outputStyle": 0, "relativeURLS": 0, "shouldRunAutoprefixer": 0, "shouldRunBless": 0, "strictImports": 0, "strictMath": 0, "strictUnits": 0}, "/assets/stylesheets/less/style.less": {"allowInsecureImports": 0, "createSourceMap": 0, "disableJavascript": 0, "fileType": 1, "ieCompatibility": 1, "ignore": 0, "ignoreWasSetByUser": 0, "inputAbbreviatedPath": "/assets/stylesheets/less/style.less", "outputAbbreviatedPath": "/assets/stylesheets/css/style.css", "outputPathIsOutsideProject": 0, "outputPathIsSetByUser": 0, "outputStyle": 0, "relativeURLS": 0, "shouldRunAutoprefixer": 0, "shouldRunBless": 0, "strictImports": 0, "strictMath": 0, "strictUnits": 0}, "/assets/stylesheets/less/variable.less": {"allowInsecureImports": 0, "createSourceMap": 0, "disableJavascript": 0, "fileType": 1, "ieCompatibility": 1, "ignore": 1, "ignoreWasSetByUser": 0, "inputAbbreviatedPath": "/assets/stylesheets/less/variable.less", "outputAbbreviatedPath": "/assets/stylesheets/css/variable.css", "outputPathIsOutsideProject": 0, "outputPathIsSetByUser": 0, "outputStyle": 0, "relativeURLS": 0, "shouldRunAutoprefixer": 0, "shouldRunBless": 0, "strictImports": 0, "strictMath": 0, "strictUnits": 0}, "/blog-center.html": {"fileType": 8192, "ignore": 1, "ignoreWasSetByUser": 0, "inputAbbreviatedPath": "/blog-center.html", "outputAbbreviatedPath": "No Output Path", "outputPathIsOutsideProject": 0, "outputPathIsSetByUser": 0}, "/blog-right-content.html": {"fileType": 8192, "ignore": 1, "ignoreWasSetByUser": 0, "inputAbbreviatedPath": "/blog-right-content.html", "outputAbbreviatedPath": "No Output Path", "outputPathIsOutsideProject": 0, "outputPathIsSetByUser": 0}, "/blog.html": {"fileType": 8192, "ignore": 1, "ignoreWasSetByUser": 0, "inputAbbreviatedPath": "/blog.html", "outputAbbreviatedPath": "No Output Path", "outputPathIsOutsideProject": 0, "outputPathIsSetByUser": 0}, "/chart-page.html": {"fileType": 8192, "ignore": 1, "ignoreWasSetByUser": 0, "inputAbbreviatedPath": "/chart-page.html", "outputAbbreviatedPath": "No Output Path", "outputPathIsOutsideProject": 0, "outputPathIsSetByUser": 0}, "/gallery-4-column.html": {"fileType": 8192, "ignore": 1, "ignoreWasSetByUser": 0, "inputAbbreviatedPath": "/gallery-4-column.html", "outputAbbreviatedPath": "No Output Path", "outputPathIsOutsideProject": 0, "outputPathIsSetByUser": 0}, "/gallery-dot.html": {"fileType": 8192, "ignore": 1, "ignoreWasSetByUser": 0, "inputAbbreviatedPath": "/gallery-dot.html", "outputAbbreviatedPath": "No Output Path", "outputPathIsOutsideProject": 0, "outputPathIsSetByUser": 0}, "/gallery.html": {"fileType": 8192, "ignore": 1, "ignoreWasSetByUser": 0, "inputAbbreviatedPath": "/gallery.html", "outputAbbreviatedPath": "No Output Path", "outputPathIsOutsideProject": 0, "outputPathIsSetByUser": 0}, "/index.html": {"fileType": 8192, "ignore": 1, "ignoreWasSetByUser": 0, "inputAbbreviatedPath": "/index.html", "outputAbbreviatedPath": "No Output Path", "outputPathIsOutsideProject": 0, "outputPathIsSetByUser": 0}, "/privacy-policy.html": {"fileType": 8192, "ignore": 1, "ignoreWasSetByUser": 0, "inputAbbreviatedPath": "/privacy-policy.html", "outputAbbreviatedPath": "No Output Path", "outputPathIsOutsideProject": 0, "outputPathIsSetByUser": 0}, "/product-details-page.html": {"fileType": 8192, "ignore": 1, "ignoreWasSetByUser": 0, "inputAbbreviatedPath": "/product-details-page.html", "outputAbbreviatedPath": "No Output Path", "outputPathIsOutsideProject": 0, "outputPathIsSetByUser": 0}, "/product-details-page2.html": {"fileType": 8192, "ignore": 1, "ignoreWasSetByUser": 0, "inputAbbreviatedPath": "/product-details-page2.html", "outputAbbreviatedPath": "No Output Path", "outputPathIsOutsideProject": 0, "outputPathIsSetByUser": 0}, "/product-details-page3.html": {"fileType": 8192, "ignore": 1, "ignoreWasSetByUser": 0, "inputAbbreviatedPath": "/product-details-page3.html", "outputAbbreviatedPath": "No Output Path", "outputPathIsOutsideProject": 0, "outputPathIsSetByUser": 0}, "/product-details-page4.html": {"fileType": 8192, "ignore": 1, "ignoreWasSetByUser": 0, "inputAbbreviatedPath": "/product-details-page4.html", "outputAbbreviatedPath": "No Output Path", "outputPathIsOutsideProject": 0, "outputPathIsSetByUser": 0}, "/shop.html": {"fileType": 8192, "ignore": 1, "ignoreWasSetByUser": 0, "inputAbbreviatedPath": "/shop.html", "outputAbbreviatedPath": "No Output Path", "outputPathIsOutsideProject": 0, "outputPathIsSetByUser": 0}, "/slim/404.slim": {"compileOnly": 0, "fileType": 1024, "ignore": 0, "ignoreWasSetByUser": 0, "inputAbbreviatedPath": "/slim/404.slim", "logicless": 0, "outputAbbreviatedPath": "/404.html", "outputFormat": 0, "outputPathIsOutsideProject": 0, "outputPathIsSetByUser": 0, "outputStyle": 0, "railsCompatible": 0}, "/slim/blog-center.slim": {"compileOnly": 0, "fileType": 1024, "ignore": 0, "ignoreWasSetByUser": 0, "inputAbbreviatedPath": "/slim/blog-center.slim", "logicless": 0, "outputAbbreviatedPath": "/blog-center.html", "outputFormat": 0, "outputPathIsOutsideProject": 0, "outputPathIsSetByUser": 0, "outputStyle": 0, "railsCompatible": 0}, "/slim/blog-right-content.slim": {"compileOnly": 0, "fileType": 1024, "ignore": 0, "ignoreWasSetByUser": 0, "inputAbbreviatedPath": "/slim/blog-right-content.slim", "logicless": 0, "outputAbbreviatedPath": "/blog-right-content.html", "outputFormat": 0, "outputPathIsOutsideProject": 0, "outputPathIsSetByUser": 0, "outputStyle": 0, "railsCompatible": 0}, "/slim/blog.slim": {"compileOnly": 0, "fileType": 1024, "ignore": 0, "ignoreWasSetByUser": 0, "inputAbbreviatedPath": "/slim/blog.slim", "logicless": 0, "outputAbbreviatedPath": "/blog.html", "outputFormat": 0, "outputPathIsOutsideProject": 0, "outputPathIsSetByUser": 0, "outputStyle": 0, "railsCompatible": 0}, "/slim/chart-page.slim": {"compileOnly": 0, "fileType": 1024, "ignore": 0, "ignoreWasSetByUser": 0, "inputAbbreviatedPath": "/slim/chart-page.slim", "logicless": 0, "outputAbbreviatedPath": "/chart-page.html", "outputFormat": 0, "outputPathIsOutsideProject": 0, "outputPathIsSetByUser": 0, "outputStyle": 0, "railsCompatible": 0}, "/slim/gallery-4-column.slim": {"compileOnly": 0, "fileType": 1024, "ignore": 0, "ignoreWasSetByUser": 0, "inputAbbreviatedPath": "/slim/gallery-4-column.slim", "logicless": 0, "outputAbbreviatedPath": "/gallery-4-column.html", "outputFormat": 0, "outputPathIsOutsideProject": 0, "outputPathIsSetByUser": 0, "outputStyle": 0, "railsCompatible": 0}, "/slim/gallery-dot.slim": {"compileOnly": 0, "fileType": 1024, "ignore": 0, "ignoreWasSetByUser": 0, "inputAbbreviatedPath": "/slim/gallery-dot.slim", "logicless": 0, "outputAbbreviatedPath": "/gallery-dot.html", "outputFormat": 0, "outputPathIsOutsideProject": 0, "outputPathIsSetByUser": 0, "outputStyle": 0, "railsCompatible": 0}, "/slim/gallery.slim": {"compileOnly": 0, "fileType": 1024, "ignore": 0, "ignoreWasSetByUser": 0, "inputAbbreviatedPath": "/slim/gallery.slim", "logicless": 0, "outputAbbreviatedPath": "/gallery.html", "outputFormat": 0, "outputPathIsOutsideProject": 0, "outputPathIsSetByUser": 0, "outputStyle": 0, "railsCompatible": 0}, "/slim/index.slim": {"compileOnly": 0, "fileType": 1024, "ignore": 0, "ignoreWasSetByUser": 0, "inputAbbreviatedPath": "/slim/index.slim", "logicless": 0, "outputAbbreviatedPath": "/index.html", "outputFormat": 0, "outputPathIsOutsideProject": 0, "outputPathIsSetByUser": 0, "outputStyle": 0, "railsCompatible": 0}, "/slim/privacy-policy.slim": {"compileOnly": 0, "fileType": 1024, "ignore": 0, "ignoreWasSetByUser": 0, "inputAbbreviatedPath": "/slim/privacy-policy.slim", "logicless": 0, "outputAbbreviatedPath": "/privacy-policy.html", "outputFormat": 0, "outputPathIsOutsideProject": 0, "outputPathIsSetByUser": 0, "outputStyle": 0, "railsCompatible": 0}, "/slim/product-details-page.slim": {"compileOnly": 0, "fileType": 1024, "ignore": 0, "ignoreWasSetByUser": 0, "inputAbbreviatedPath": "/slim/product-details-page.slim", "logicless": 0, "outputAbbreviatedPath": "/product-details-page.html", "outputFormat": 0, "outputPathIsOutsideProject": 0, "outputPathIsSetByUser": 0, "outputStyle": 0, "railsCompatible": 0}, "/slim/product-details-page2.slim": {"compileOnly": 0, "fileType": 1024, "ignore": 0, "ignoreWasSetByUser": 0, "inputAbbreviatedPath": "/slim/product-details-page2.slim", "logicless": 0, "outputAbbreviatedPath": "/product-details-page2.html", "outputFormat": 0, "outputPathIsOutsideProject": 0, "outputPathIsSetByUser": 0, "outputStyle": 0, "railsCompatible": 0}, "/slim/product-details-page3.slim": {"compileOnly": 0, "fileType": 1024, "ignore": 0, "ignoreWasSetByUser": 0, "inputAbbreviatedPath": "/slim/product-details-page3.slim", "logicless": 0, "outputAbbreviatedPath": "/product-details-page3.html", "outputFormat": 0, "outputPathIsOutsideProject": 0, "outputPathIsSetByUser": 0, "outputStyle": 0, "railsCompatible": 0}, "/slim/product-details-page4.slim": {"compileOnly": 0, "fileType": 1024, "ignore": 0, "ignoreWasSetByUser": 0, "inputAbbreviatedPath": "/slim/product-details-page4.slim", "logicless": 0, "outputAbbreviatedPath": "/product-details-page4.html", "outputFormat": 0, "outputPathIsOutsideProject": 0, "outputPathIsSetByUser": 0, "outputStyle": 0, "railsCompatible": 0}, "/slim/shop.slim": {"compileOnly": 0, "fileType": 1024, "ignore": 0, "ignoreWasSetByUser": 0, "inputAbbreviatedPath": "/slim/shop.slim", "logicless": 0, "outputAbbreviatedPath": "/shop.html", "outputFormat": 0, "outputPathIsOutsideProject": 0, "outputPathIsSetByUser": 0, "outputStyle": 0, "railsCompatible": 0}, "/slim/terms-of-use.slim": {"compileOnly": 0, "fileType": 1024, "ignore": 0, "ignoreWasSetByUser": 0, "inputAbbreviatedPath": "/slim/terms-of-use.slim", "logicless": 0, "outputAbbreviatedPath": "/terms-of-use.html", "outputFormat": 0, "outputPathIsOutsideProject": 0, "outputPathIsSetByUser": 0, "outputStyle": 0, "railsCompatible": 0}, "/terms-of-use.html": {"fileType": 8192, "ignore": 1, "ignoreWasSetByUser": 0, "inputAbbreviatedPath": "/terms-of-use.html", "outputAbbreviatedPath": "No Output Path", "outputPathIsOutsideProject": 0, "outputPathIsSetByUser": 0}}, "hooks": [], "lastSavedByUser": "<PERSON><PERSON>", "manualImportLinks": {}, "projectAttributes": {"bowerAbbreviatedPath": "", "displayValue": "cakesdream", "displayValueWasSetByUser": 0, "iconImageName": "compass_yellow"}, "projectSettings": {"alwaysUseExternalServer": 0, "animateCSSInjections": 1, "autoApplyPSLanguageSettingsStyle": 0, "autoprefixerBrowserString": "> 1%, last 2 versions, Firefox ESR, Opera 12.1", "autoSyncProjectSettingsFile": 1, "browserRefreshDelay": 0, "coffeeAutoOutputPathEnabled": 1, "coffeeAutoOutputPathFilenamePattern": "*.js", "coffeeAutoOutputPathRelativePath": "", "coffeeAutoOutputPathReplace1": "", "coffeeAutoOutputPathReplace2": "", "coffeeAutoOutputPathStyle": 0, "coffeeCreateSourceMap": 0, "coffeeLintFlags2": {"arrow_spacing": {"active": 0, "flagValue": -1}, "camel_case_classes": {"active": 1, "flagValue": -1}, "colon_assignment_spacing": {"active": 0, "flagValue": 1}, "cyclomatic_complexity": {"active": 0, "flagValue": 10}, "duplicate_key": {"active": 1, "flagValue": -1}, "empty_constructor_needs_parens": {"active": 0, "flagValue": -1}, "ensure_comprehensions": {"active": 1, "flagValue": -1}, "indentation": {"active": 1, "flagValue": 2}, "line_endings": {"active": 0, "flagValue": 0}, "max_line_length": {"active": 0, "flagValue": 150}, "missing_fat_arrows": {"active": 0, "flagValue": -1}, "newlines_after_classes": {"active": 0, "flagValue": 3}, "no_backticks": {"active": 1, "flagValue": -1}, "no_debugger": {"active": 1, "flagValue": -1}, "no_empty_functions": {"active": 0, "flagValue": -1}, "no_empty_param_list": {"active": 0, "flagValue": -1}, "no_implicit_braces": {"active": 1, "flagValue": -1}, "no_implicit_parens": {"active": 0, "flagValue": -1}, "no_interpolation_in_single_quotes": {"active": 0, "flagValue": -1}, "no_plusplus": {"active": 0, "flagValue": -1}, "no_stand_alone_at": {"active": 1, "flagValue": -1}, "no_tabs": {"active": 1, "flagValue": -1}, "no_throwing_strings": {"active": 1, "flagValue": -1}, "no_trailing_semicolons": {"active": 1, "flagValue": -1}, "no_trailing_whitespace": {"active": 1, "flagValue": -1}, "no_unnecessary_double_quotes": {"active": 0, "flagValue": -1}, "no_unnecessary_fat_arrows": {"active": 1, "flagValue": -1}, "non_empty_constructor_needs_parens": {"active": 0, "flagValue": -1}, "prefer_english_operator": {"active": 0, "flagValue": -1}, "space_operators": {"active": 0, "flagValue": -1}, "spacing_after_comma": {"active": 1, "flagValue": -1}}, "coffeeMinifyOutput": 1, "coffeeOutputStyle": 0, "coffeeSyntaxCheckerStyle": 1, "externalServerAddress": "http://localhost:8888", "externalServerPreviewPathAddition": "", "genericWebpageFileExtensionsString": "html, htm, shtml, shtm, xhtml, php, jsp, asp, aspx, erb, ctp", "hamlAutoOutputPathEnabled": 1, "hamlAutoOutputPathFilenamePattern": "*.html", "hamlAutoOutputPathRelativePath": "", "hamlAutoOutputPathReplace1": "", "hamlAutoOutputPathReplace2": "", "hamlAutoOutputPathStyle": 0, "hamlEscapeHTMLCharacters": 0, "hamlNoEscapeInAttributes": 0, "hamlOutputFormat": 2, "hamlOutputStyle": 0, "hamlUseCDATA": 0, "hamlUseDoubleQuotes": 0, "hamlUseUnixNewlines": 0, "jadeAutoOutputPathEnabled": 1, "jadeAutoOutputPathFilenamePattern": "*.html", "jadeAutoOutputPathRelativePath": "", "jadeAutoOutputPathReplace1": "", "jadeAutoOutputPathReplace2": "", "jadeAutoOutputPathStyle": 0, "jadeCompileDebug": 1, "jadeOutputStyle": 0, "javascriptAutoOutputPathEnabled": 1, "javascriptAutoOutputPathFilenamePattern": "*-min.js", "javascriptAutoOutputPathRelativePath": "/min", "javascriptAutoOutputPathReplace1": "", "javascriptAutoOutputPathReplace2": "", "javascriptAutoOutputPathStyle": 2, "javascriptCreateSourceMap": 1, "javascriptOutputStyle": 1, "javascriptSyntaxCheckerStyle": 1, "jsCheckerReservedNamesString": "", "jsHintFlags2": {"asi": {"active": 0, "flagValue": -1}, "bitwise": {"active": 1, "flagValue": -1}, "boss": {"active": 0, "flagValue": -1}, "browser": {"active": 1, "flagValue": -1}, "browserify": {"active": 0, "flagValue": -1}, "camelcase": {"active": 0, "flagValue": -1}, "couch": {"active": 0, "flagValue": -1}, "curly": {"active": 1, "flagValue": -1}, "debug": {"active": 0, "flagValue": -1}, "devel": {"active": 0, "flagValue": -1}, "dojo": {"active": 0, "flagValue": -1}, "elision": {"active": 1, "flagValue": -1}, "eqeqeq": {"active": 1, "flagValue": -1}, "eqnull": {"active": 0, "flagValue": -1}, "es3": {"active": 0, "flagValue": -1}, "esnext": {"active": 0, "flagValue": -1}, "evil": {"active": 0, "flagValue": -1}, "expr": {"active": 0, "flagValue": -1}, "forin": {"active": 0, "flagValue": -1}, "freeze": {"active": 1, "flagValue": -1}, "funcscope": {"active": 0, "flagValue": -1}, "globalstrict": {"active": 0, "flagValue": -1}, "immed": {"active": 0, "flagValue": -1}, "indent": {"active": 0, "flagValue": 4}, "iterator": {"active": 0, "flagValue": -1}, "jasmine": {"active": 0, "flagValue": -1}, "jquery": {"active": 1, "flagValue": -1}, "lastsemic": {"active": 0, "flagValue": -1}, "latedef": {"active": 1, "flagValue": -1}, "laxbreak": {"active": 0, "flagValue": -1}, "laxcomma": {"active": 0, "flagValue": -1}, "loopfunc": {"active": 0, "flagValue": -1}, "maxcomplexity": {"active": 0, "flagValue": 10}, "maxdepth": {"active": 0, "flagValue": 3}, "maxlen": {"active": 0, "flagValue": 150}, "maxparams": {"active": 0, "flagValue": 3}, "maxstatements": {"active": 0, "flagValue": 4}, "mocha": {"active": 0, "flagValue": -1}, "mootools": {"active": 0, "flagValue": -1}, "moz": {"active": 0, "flagValue": -1}, "multistr": {"active": 0, "flagValue": -1}, "newcap": {"active": 1, "flagValue": -1}, "noarg": {"active": 1, "flagValue": -1}, "node": {"active": 0, "flagValue": -1}, "noempty": {"active": 0, "flagValue": -1}, "nonbsp": {"active": 0, "flagValue": -1}, "nonew": {"active": 1, "flagValue": -1}, "nonstandard": {"active": 0, "flagValue": -1}, "notypeof": {"active": 1, "flagValue": -1}, "noyield": {"active": 0, "flagValue": -1}, "onecase": {"active": 0, "flagValue": -1}, "phantom": {"active": 0, "flagValue": -1}, "plusplus": {"active": 0, "flagValue": -1}, "proto": {"active": 0, "flagValue": -1}, "prototypejs": {"active": 0, "flagValue": -1}, "qunit": {"active": 0, "flagValue": -1}, "regexp": {"active": 1, "flagValue": -1}, "rhino": {"active": 0, "flagValue": -1}, "scripturl": {"active": 0, "flagValue": -1}, "shadow": {"active": 0, "flagValue": -1}, "shelljs": {"active": 0, "flagValue": -1}, "singleGroups": {"active": 0, "flagValue": -1}, "strict": {"active": 0, "flagValue": -1}, "sub": {"active": 0, "flagValue": -1}, "supernew": {"active": 0, "flagValue": -1}, "typed": {"active": 0, "flagValue": -1}, "undef": {"active": 1, "flagValue": -1}, "unused": {"active": 1, "flagValue": -1}, "withstmt": {"active": 0, "flagValue": -1}, "worker": {"active": 0, "flagValue": -1}, "wsh": {"active": 0, "flagValue": -1}, "yui": {"active": 0, "flagValue": -1}}, "jsLintFlags2": {"ass": {"active": 0, "flagValue": -1}, "bitwise": {"active": 0, "flagValue": -1}, "browser": {"active": 1, "flagValue": -1}, "closure": {"active": 0, "flagValue": -1}, "continue": {"active": 0, "flagValue": -1}, "debug": {"active": 0, "flagValue": -1}, "devel": {"active": 0, "flagValue": -1}, "eqeq": {"active": 0, "flagValue": -1}, "evil": {"active": 0, "flagValue": -1}, "forin": {"active": 0, "flagValue": -1}, "indent": {"active": 0, "flagValue": 4}, "maxlen": {"active": 0, "flagValue": 150}, "newcap": {"active": 0, "flagValue": -1}, "node": {"active": 0, "flagValue": -1}, "nomen": {"active": 0, "flagValue": -1}, "plusplus": {"active": 0, "flagValue": -1}, "properties": {"active": 0, "flagValue": -1}, "regexp": {"active": 0, "flagValue": -1}, "rhino": {"active": 0, "flagValue": -1}, "sloppy": {"active": 0, "flagValue": -1}, "stupid": {"active": 0, "flagValue": -1}, "sub": {"active": 0, "flagValue": -1}, "todo": {"active": 0, "flagValue": -1}, "unparam": {"active": 0, "flagValue": -1}, "vars": {"active": 0, "flagValue": -1}, "white": {"active": 0, "flagValue": -1}}, "kitAutoOutputPathEnabled": 1, "kitAutoOutputPathFilenamePattern": "*.html", "kitAutoOutputPathRelativePath": "", "kitAutoOutputPathReplace1": "", "kitAutoOutputPathReplace2": "", "kitAutoOutputPathStyle": 0, "lessAllowInsecureImports": 0, "lessAutoOutputPathEnabled": 1, "lessAutoOutputPathFilenamePattern": "*.css", "lessAutoOutputPathRelativePath": "../css", "lessAutoOutputPathReplace1": "less", "lessAutoOutputPathReplace2": "css", "lessAutoOutputPathStyle": 2, "lessCreateSourceMap": 0, "lessDisableJavascript": 0, "lessIeCompatibility": 1, "lessOutputStyle": 0, "lessRelativeURLS": 0, "lessStrictImports": 0, "lessStrictMath": 0, "lessStrictUnits": 0, "markdownAutoOutputPathEnabled": 1, "markdownAutoOutputPathFilenamePattern": "*.html", "markdownAutoOutputPathRelativePath": "", "markdownAutoOutputPathReplace1": "", "markdownAutoOutputPathReplace2": "", "markdownAutoOutputPathStyle": 0, "markdownEnableFootnotes": 0, "markdownEnableSmartyPants": 1, "markdownExpandTabs": 1, "reloadFileURLs": 0, "sassAutoOutputPathEnabled": 1, "sassAutoOutputPathFilenamePattern": "*.css", "sassAutoOutputPathRelativePath": "../css", "sassAutoOutputPathReplace1": "sass", "sassAutoOutputPathReplace2": "css", "sassAutoOutputPathStyle": 2, "sassCreateSourceMap": 0, "sassDebugStyle": 0, "sassDecimalPrecision": 10, "sassOutputStyle": 0, "sassUseLibsass": 0, "shouldRunAutoprefixer": 0, "shouldRunBless": 0, "skippedItemsString": ".svn, .git, .hg, log, _logs, _cache, cache, logs, node_modules", "slimAutoOutputPathEnabled": 1, "slimAutoOutputPathFilenamePattern": "*.html", "slimAutoOutputPathRelativePath": "", "slimAutoOutputPathReplace1": "", "slimAutoOutputPathReplace2": "", "slimAutoOutputPathStyle": 2, "slimCompileOnly": 0, "slimLogicless": 0, "slimOutputFormat": 0, "slimOutputStyle": 0, "slimRailsCompatible": 0, "stylusAutoOutputPathEnabled": 1, "stylusAutoOutputPathFilenamePattern": "*.css", "stylusAutoOutputPathRelativePath": "../css", "stylusAutoOutputPathReplace1": "stylus", "stylusAutoOutputPathReplace2": "css", "stylusAutoOutputPathStyle": 2, "stylusCreateSourceMap": 0, "stylusDebugStyle": 0, "stylusImportCSS": 0, "stylusOutputStyle": 0, "stylusResolveRelativeURLS": 0, "typescriptAutoOutputPathEnabled": 1, "typescriptAutoOutputPathFilenamePattern": "*.js", "typescriptAutoOutputPathRelativePath": "/js", "typescriptAutoOutputPathReplace1": "", "typescriptAutoOutputPathReplace2": "", "typescriptAutoOutputPathStyle": 2, "typescriptCreateDeclarationFile": 0, "typescriptCreateSourceMap": 0, "typescriptMinifyOutput": 0, "typescriptModuleType": 0, "typescriptNoImplicitAny": 0, "typescriptPreserveConstEnums": 0, "typescriptRemoveComments": 0, "typescriptSuppressImplicitAnyIndexErrors": 0, "typescriptTargetECMAVersion": 0, "uglifyDefinesString": "", "uglifyFlags2": {"ascii-only": {"active": 0, "flagValue": -1}, "booleans": {"active": 1, "flagValue": -1}, "bracketize": {"active": 0, "flagValue": -1}, "cascade": {"active": 1, "flagValue": -1}, "comments": {"active": 1, "flagValue": -1}, "comparisons": {"active": 1, "flagValue": -1}, "compress": {"active": 1, "flagValue": -1}, "conditionals": {"active": 1, "flagValue": -1}, "dead_code": {"active": 0, "flagValue": -1}, "drop_console": {"active": 0, "flagValue": -1}, "drop_debugger": {"active": 1, "flagValue": -1}, "eval": {"active": 0, "flagValue": -1}, "evaluate": {"active": 1, "flagValue": -1}, "hoist_funs": {"active": 1, "flagValue": -1}, "hoist_vars": {"active": 0, "flagValue": -1}, "if_return": {"active": 1, "flagValue": -1}, "indent-level": {"active": 0, "flagValue": 4}, "indent-start": {"active": 0, "flagValue": 0}, "inline-script": {"active": 0, "flagValue": -1}, "join_vars": {"active": 1, "flagValue": -1}, "keep_fargs": {"active": 0, "flagValue": -1}, "loops": {"active": 1, "flagValue": -1}, "mangle": {"active": 1, "flagValue": -1}, "max-line-len": {"active": 1, "flagValue": 32000}, "negate_iife": {"active": 1, "flagValue": -1}, "properties": {"active": 1, "flagValue": -1}, "pure_getters": {"active": 0, "flagValue": -1}, "quote-keys": {"active": 0, "flagValue": -1}, "screw-ie8": {"active": 0, "flagValue": -1}, "semicolons": {"active": 1, "flagValue": -1}, "sequences": {"active": 1, "flagValue": -1}, "sort": {"active": 0, "flagValue": -1}, "space-colon": {"active": 1, "flagValue": -1}, "toplevel": {"active": 0, "flagValue": -1}, "unsafe": {"active": 0, "flagValue": -1}, "unused": {"active": 0, "flagValue": -1}, "warnings": {"active": 0, "flagValue": -1}, "width": {"active": 1, "flagValue": 80}}, "uglifyReservedNamesString": "$", "websiteRelativeRoot": ""}, "settingsFileVersion": "2"}