<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Financial Charts</title>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        body {
            font-family: Arial, sans-serif;
            display: flex;
            flex-direction: column;
            align-items: center;
            background-color: #f0f0f0;
        }
        .chart-container {
            width: 80%;
            max-width: 800px;
            margin: 20px 0;
            background-color: white;
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            padding: 20px;
        }
    </style>
</head>
<body>
    <div class="chart-container">
        <canvas id="goldChart"></canvas>
    </div>
    <div class="chart-container">
        <canvas id="dogeChart"></canvas>
    </div>
    <div class="chart-container">
        <canvas id="usdthbChart"></canvas>
    </div>

    <script>
        // Function to create a chart
        function createChart(ctx, label, data, color) {
            return new Chart(ctx, {
                type: 'line',
                data: {
                    labels: data.map(item => item.time),
                    datasets: [{
                        label: label,
                        data: data.map(item => item.value),
                        borderColor: color,
                        tension: 0.1
                    }]
                },
                options: {
                    responsive: true,
                    scales: {
                        y: {
                            beginAtZero: false
                        }
                    }
                }
            });
        }

        // Function to fetch data and update chart
        async function updateChart(url, chartId, label, color) {
            try {
                const response = await fetch(url);
                const data = await response.json();
                const ctx = document.getElementById(chartId).getContext('2d');
                createChart(ctx, label, data, color);
            } catch (error) {
                console.error('Error fetching data:', error);
            }
        }

        // Update charts
        updateChart('https://api.metalpriceapi.com/v1/latest?api_key=YOUR_API_KEY&base=XAU&currencies=USD', 'goldChart', 'Gold Price (USD/oz)', 'gold');
        updateChart('https://api.coingecko.com/api/v3/coins/dogecoin/market_chart?vs_currency=usd&days=30', 'dogeChart', 'Dogecoin Price (USD)', 'orange');
        updateChart('https://api.exchangerate-api.com/v4/latest/USD', 'usdthbChart', 'USD/THB Exchange Rate', 'green');
    </script>
</body>
</html>
